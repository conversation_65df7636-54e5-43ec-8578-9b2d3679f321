import frappe
from frappe.model.document import Document
import requests
import json
from frappe.utils import nowdate, formatdate, get_site_path, now_datetime, get_datetime
from frappe import _
from jinja2 import Environment, FileSystemLoader, StrictUndefined
import os
from frappe.utils.pdf import get_pdf
from frappe.utils.response import build_response
from frappe.utils.file_manager import save_file
import mimetypes
from frappe.utils import get_files_path
from datetime import date, datetime
from frappe.utils.print_format import get_pdf
import math


from westside.www.Authentication.auth_decorators import role_required

from westside.westside.doctype.quickbooks_settings.quickbooks_settings import  get_quickbooks_access_token
from westside.westside.doctype.document_number_generate.document_number_generate import get_document_number


class InvoiceDB(Document):
	pass


@frappe.whitelist(allow_guest=True)
def get_all_customers():
    """
    Returns a list of all customers.
    """
    try:
        filters = {}
        filters["is_active"] = True
        
        customers_list = frappe.get_all(
            "Customer DB",
            filters=filters,
            fields=[
                "name", "customer_name"
            ],
        )

        response_data = []
        for customer in customers_list:
            customers_data = {
                "id": customer.name,
                "customer_name": customer.customer_name
            }
            response_data.append(customers_data)

        return {'status': "success", 'Message': 'Customers list fetched successfully', 'data': response_data, 'status_code': 200}
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Get vendors API Error")
        return {'status': "error", "Message": f'Internal Server Error: {str(e)}', 'status_code': 500}


# @role_required(["Admin"])
@frappe.whitelist(allow_guest=True)
def get_docket_details(docket_id):
    """
    Returns the details of a docket.
    """
    try:
        if not docket_id:
            return {
                "status": "error",
                "Message": "Docket ID is required",
                "status_code": 400
            }

        docket = frappe.get_doc("Docket DB", docket_id)
        if not docket:
            return {'status': "error", 'Message': 'Docket not found', 'status_code': 404}

        booking_doc = frappe.get_doc("Booking Request", docket.booking)

        customer_name = ""
        if docket.customer_id:
            customer_name = frappe.get_value("Customer DB", docket.customer_id, "customer_name")

        docket_data = {
            "docket_id": docket.name,
            "booking": docket.booking,
            "carrier_booking_number": booking_doc.carrier_booking_number,
            "customer": docket.customer,
            "customer_id": docket.customer_id,
            "customer_name": customer_name,  
            "shipping_date": docket.shipping_date,
            "invoice": docket.invoice,
            "hs_code": docket.hs_code,
            "origin": docket.origin,
            "destination": docket.destination,
            "blno": docket.blno,
            "terms": docket.terms,
            "status": docket.status,
            "customer_invoice_id":docket.customer_invoice_id
        }

        return {
            "status": "success",
            "status_code": 200,
            "Message": "Docket details fetched successfully",
            "data": docket_data
        }

    except frappe.DoesNotExistError:
        return {
            "status": "error",
            "Message": "Docket not found",
            "status_code": 404
        }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Get Docket Detail API Error")
        return {
            "status": "error",
            "status_code": 500,
            "Message": {"error": str(e)}
        }



@frappe.whitelist(allow_guest=True)
def get_quickbooks_customers():
    """
    Returns a list of all customers from QuickBooks via QuickBooks API.
    """
    try:
        settings = frappe.get_single("QuickBooks Settings")
        realm_id = settings.realm_id
        api_url = settings.api_url
        minor_version = settings.minor_version

        access_token = frappe.cache().get_value("quickbooks_access_token")
        if not access_token:
            token_data = get_quickbooks_access_token()
            if not token_data:
                return {
                    "status": "error",
                    "Message": "Token refresh failed.",
                    "status_code": 500
                }
            access_token = token_data["access_token"]

        url = f"{api_url}/v3/company/{realm_id}/query?query=SELECT * FROM Customer WHERE Active = True&minorversion={minor_version}"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json"
        }

        response = requests.get(url, headers=headers)
        if response.status_code != 200:
            frappe.log_error(response.text, "Fetch QuickBooks Customers Failed")
            return {
                "status": "error",
                "Message": "Failed to fetch customers from QuickBooks.",
                "status_code": 500
            }

        qb_customers = response.json().get("QueryResponse", {}).get("Customer", [])
        
        customers = []
        for c in qb_customers:
            customers.append({
                "id": c.get("Id"),
                "display_name": c.get("DisplayName"),
                "company_name": c.get("CompanyName"),
                "email": c.get("PrimaryEmailAddr", {}).get("Address"),
                "phone": c.get("PrimaryPhone", {}).get("FreeFormNumber"),
                "billing_address": c.get("BillAddr", {}),
                "shipping_address": c.get("ShipAddr", {})
            })

        return {
            "status": "success",
            "Message": "Customers fetched successfully",
            "status_code": 200,
            "customers": customers}

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Error in get_quickbooks_customers")
        return {
            "status": "error",
            "Message": f"Error fetching QuickBooks customers. {str(e)}",
            "status_code": 500
        }
        

@frappe.whitelist(allow_guest=True)
def get_quickbooks_items(search_type=None):
    """
    Returns a list of all items from QuickBooks via QuickBooks API.
    """
    try:
        access_token = frappe.cache().get_value("quickbooks_access_token")
        if not access_token:
            token_data = get_quickbooks_access_token()
            if not token_data:
                return {
                    "status": "error",
                    "Message": "Token refresh failed.",
                    "status_code": 500
                }
            access_token = token_data["access_token"]

        settings = frappe.get_single("QuickBooks Settings")
        realm_id = settings.realm_id
        api_url = settings.api_url

        items = frappe.cache().get_value("cached_items")
        if not items:
            url = f"{api_url}/v3/company/{realm_id}/query?query=SELECT * FROM Item WHERE Active = True MAXRESULTS 1000"
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Accept": "application/json"
            }

            resp = requests.get(url, headers=headers)
            if resp.status_code != 200:
                frappe.log_error(resp.text, "QuickBooks Item Fetch Failed")
                return {
                    "status": "error",
                    "Message": "Failed to fetch items from QuickBooks",
                    "status_code": 500
                }

            items = resp.json().get("QueryResponse", {}).get("Item", [])
            frappe.cache().set_value("cached_items", items, expires_in_sec=3600)
        filtered_items = []
        if items and search_type:
            filtered_items = [
                item for item in items
                if any(
                    search_type.lower() in str(value).lower()
                    for value in item.values()
                    if isinstance(value, str) or isinstance(value, int)  
                )
            ]
        if items and not filtered_items:
            filtered_items = items

        item_list = [
            {
                "id": item.get("Id"),
                "name": item.get("Name"),
                "type": item.get("Type"),
                "unit_price": item.get("UnitPrice"),
                "description": item.get("Description")
            }
            for item in filtered_items
        ]

        return {
            "status": "success",            
            "Message": "Items fetched successfully",
            "status_code": 200,
            "items": item_list
        }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Get QuickBooks Items Failed")
        return {
            "status": "error",
            "Message": "Something went wrong while fetching QuickBooks items.",
            "status_code": 500
        }

# def generate_custom_invoice_number():
#     current_year = now_datetime().year
#     prefix = f"WS-{current_year}-"

#     last_invoice = frappe.db.sql("""
#         SELECT invoice_number FROM `tabInvoice DB`
#         WHERE invoice_number LIKE %s
#         ORDER BY creation DESC
#         LIMIT 1
#     """, (prefix + '%',), as_dict=True)

#     if last_invoice:
#         try:
#             last_seq = int(last_invoice[0]["invoice_number"].split("-")[-1])
#             new_seq = last_seq + 1
#         except Exception:
#             new_seq = 1
#     else:
#         new_seq = 1

#     return f"{prefix}{str(new_seq).zfill(4)}"


@frappe.whitelist(allow_guest=True)
def create_quickbooks_invoice():
    """
    Creates an invoice in QuickBooks via QuickBooks API.
    """
    try:
        json_data = frappe.form_dict.get("data")
        if not json_data:
            return {
                "status_code": 400,
                "Message": "Missing JSON payload in 'data' field."
            }

        try:
            payload = json.loads(json_data)
        except Exception:
            return {
                "status_code": 400,
                "Message": "Invalid JSON format in payload."
            }

        customer_id = payload.get("customer_id")
        qb_customer_id = payload.get("quickbooks_customer_id")
        items = payload.get("items", [])
        invoice_data = payload.get("invoice_data", {})

        if not qb_customer_id or not items:
            return {
                "status_code": 400,
                "Message": "QuickBooks Customer and items are required."
            }

        line_items = []
        for item in items:
            quantity = float(item.get("quantity", 0))
            rate = float(item.get("rate", 0))
            amount = quantity * rate

            line_items.append({
                "DetailType": "SalesItemLineDetail",
                "Amount": amount,
                "SalesItemLineDetail": {
                    "ItemRef": {
                        "value": item.get("item_ref_id"),
                        "name": item.get("product_services")
                    },
                    "Qty": quantity,
                    "UnitPrice": rate
                },
                "Description": item.get("product_description")
            })


        qb_invoice_payload = {
            "CustomerRef": {"value": qb_customer_id},
            "Line": line_items,
            "TxnDate": invoice_data.get("invoice_date") or nowdate(),
            "DueDate": invoice_data.get("due_date"),
            "PrivateNote": invoice_data.get("comments", ""),
            "ShipDate": invoice_data.get("shipping_date") or invoice_data.get("invoice_date") or nowdate(),
            "TxnTaxDetail": { 
                "TxnTaxCodeRef": {"value": "NON"}
            }
        }


        settings = frappe.get_single("QuickBooks Settings")
        realm_id = settings.realm_id
        api_url = settings.api_url
        minor_version = settings.minor_version
        access_token = frappe.cache().get_value("quickbooks_access_token")

        if not access_token:
            token_data = get_quickbooks_access_token()
            if not token_data:
                return {
                    "status_code": 500,
                    "status": "error",
                    "Message": "Token refresh failed."
                }
            access_token = token_data["access_token"]

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json",
            "Content-Type": "application/json"
        }

        url = f"{api_url}/v3/company/{realm_id}/invoice?minorversion={minor_version}"
        custom_doc_number = get_document_number("Quickbook Invoice", "WS")

        qb_invoice_payload["DocNumber"] = custom_doc_number

        resp = requests.post(url, headers=headers, json=qb_invoice_payload)

        if resp.status_code not in (200, 201):
            frappe.log_error(resp.text, "QuickBooks Invoice Creation Failed")
            return {
                "status_code": 500,
                "status": "error",
                "Message": "Failed to create invoice in QuickBooks."
            }

        qb_invoice = resp.json().get("Invoice")
        # for i, line in enumerate(qb_invoice.get("Line", [])):
        #     if line.get("DetailType") == "SalesItemLineDetail" and i < len(invoice.items):
        #         # Set QuickBooks line ID in child table
        #         invoice.items[i].quickbooks_line_id = line.get("Id")
        qb_invoice_id = qb_invoice.get("Id")
        qb_doc_number = qb_invoice.get("DocNumber")
        qb_invoice_link = f"https://app.qbo.intuit.com/app/invoice?txnId={qb_invoice_id}"

        line_items = qb_invoice.get("Line", [])
        sub_total = 0.0
        total_tax = qb_invoice.get("TxnTaxDetail", {}).get("TotalTax", 0.0)
        total_amt = qb_invoice.get("TotalAmt", 0.0)
        balance = qb_invoice.get("Balance", 0.0)

        for line in line_items:
            if line.get("DetailType") == "SubTotalLineDetail":
                sub_total = line.get("Amount", 0.0)
                break

        # email_status = qb_invoice.get("EmailStatus", "")  
        # viewed_on = qb_invoice.get("MetaData", {}).get("LastUpdatedTime", "") 
        # deposit_to_account = qb_invoice.get("DepositToAccountRef", {}).get("value", "")  



        status = "Pending Payment"
        # if balance == 0:
        #     status = "Paid"
        # elif deposit_to_account:
        #     status = "Deposited"
        # elif viewed_on:
        #     status = "Viewed"
        # elif email_status == "EmailSent":
        #     status = "Sent"



        invoice = frappe.new_doc("Invoice DB")
        invoice.customer_id = customer_id
        invoice.quickbooks_customer_id = qb_customer_id
        invoice.quickbooks_customer_name = invoice_data.get("quickbooks_customer_name")  
        invoice.email_address = invoice_data.get("email_address")
        invoice.contact_number = invoice_data.get("contact_number")
        invoice.address = invoice_data.get("address")
        invoice.due_date = invoice_data.get("due_date")
        invoice.invoice_date = invoice_data.get("invoice_date") or nowdate() 
        invoice.quickbooks_invoice_id = qb_invoice_id
        invoice.quickbooks_invoice_link = qb_invoice_link
        invoice.invoice_number = custom_doc_number
        invoice.quickbooks_doc_number = qb_doc_number
        invoice.total_amount = total_amt
        invoice.tax = total_tax
        invoice.bol = invoice_data.get("bol")
        invoice.shipping_date = invoice_data.get("shipping_date")
        invoice.hs_code = invoice_data.get("hs_code")
        invoice.orgin_port = invoice_data.get("orgin_port")
        invoice.destination_port = invoice_data.get("destination_port")
        invoice.incotern = invoice_data.get("terms") or invoice_data.get("incoterm")
        invoice.bill_to = invoice_data.get("bill_to")
        invoice.uom = invoice_data.get("uom")
        invoice.sub_total = sub_total
        invoice.status = status
        invoice.is_active = 1



        carrier_booking_number = invoice_data.get("booking_id")  
        invoice.carrier_booking_number = carrier_booking_number

        booking_request_name = frappe.db.get_value(
            "Booking Request",
            {"carrier_booking_number": carrier_booking_number},
            "name"
        )

        if booking_request_name:
            invoice.booking_id = booking_request_name
        else:
            frappe.log_error(
                f"Carrier Booking Number {carrier_booking_number} not found in Booking Request",
                "Booking Lookup Failed"
            )

        invoice.docket_id = invoice_data.get("docket_id")
        invoice.comments = invoice_data.get("comments")
        invoice.invoice_pdf_name = f"invoice_{invoice.name}.pdf"
        invoice.payload = json.dumps(payload)
        invoice.response_data = json.dumps(resp.json())

        for i in items:
            invoice.append("items", {
                "product_services": i.get("product_services"),
                "product_description": i.get("product_description"),
                "quantity": i.get("quantity"),
                "rate": i.get("rate"),
                "amount": i.get("quantity") * i.get("rate"),
                "payload_data": json.dumps(i),
                "item_ref_id": i.get("item_ref_id"),
                "uom": i.get("uom"),
                "item_id": i.get("item_ref_id")
            })

        invoice.insert()
        docket_id = invoice_data.get("docket_id")
        if docket_id:
            try:
                docket = frappe.get_doc("Docket DB", docket_id)
                docket.invoice = qb_doc_number
                docket.customer_invoice_id = invoice.name
                docket.save(ignore_permissions=True)
            except Exception as e:
                frappe.log_error(frappe.get_traceback(), f"Failed to update Docket DB for Docket ID: {docket_id}")

        uploaded_status = {}
        attachments = frappe.request.files.getlist("attachments")
        if attachments:
            uploaded_status = upload_invoice_attachments_to_qb(qb_invoice_id, attachments, invoice.name)
        invoice_pdf = generate_invoice_pdf(invoice.name)

        return {
            "status": "success",
            "Message": "Invoice created in QuickBooks successfully.",
            "status_code": 200,
            "invoice_name": invoice.name,
            "quickbooks_invoice_id": qb_invoice_id,
            "quickbooks_invoice_link": qb_invoice_link,
            "quickbooks_doc_number": qb_doc_number,
            "total_amount": total_amt,
            "tax": total_tax,
            "file_upload_status": uploaded_status,
            "invoice_pdf": invoice_pdf
        }

    except Exception:
        frappe.log_error(frappe.get_traceback(), "Create QuickBooks Invoice Failed")
        return {
            "status": "error",
            "Message": "Something went wrong while creating invoice.",
            "status_code": 500
        }



def upload_invoice_attachments_to_qb(qb_invoice_id, lst_attachments, invoice_id):
    """
    Attach files to the QuickBooks invoice using the QuickBooks API.
    
    """
    try:
        settings = frappe.get_single("QuickBooks Settings")
        access_token = settings.access_token
        realm_id = settings.realm_id
        api_url = settings.api_url
        minor_version = settings.minor_version

        if not qb_invoice_id:
            return {
                "status_code": 400,
                "Message": "Missing QuickBooks Invoice ID.",
                "details": "qb_invoice_id is required to upload attachments."
            }

        if not access_token:
            resp_token = get_quickbooks_access_token()
            if not resp_token:
                return {
                    "status_code": 401,
                    "Message": "Token refresh failed",
                    "details": "Could not refresh QuickBooks access token."
                }
            access_token = resp_token['access_token']

        if not lst_attachments:
            return {
                "status_code": 400,
                "Message": "No attachments provided"
            }

        results = []
        for file_obj in lst_attachments:
            file_content = file_obj.stream.read()
            file_name = file_obj.filename
            mime_type = mimetypes.guess_type(file_name)[0] or "application/octet-stream"

            files = {
                "file_content_0": (file_name, file_content, mime_type),
                "file_metadata_0": (
                    "metadata.json",
                    json.dumps({
                        "AttachableRef": [{
                            "EntityRef": {
                                "type": "Invoice",
                                "value": qb_invoice_id
                            }
                        }],
                        "Category": "Document",
                        "Tag": "Invoice Attachment"
                    }),
                    "application/json"
                )
            }

            headers = {
                "Authorization": f"Bearer {access_token}",
                "Accept": "application/json"
            }

            attach_url = f"{api_url}/v3/company/{realm_id}/upload?minorversion={minor_version}"
            qb_resp = requests.post(attach_url, headers=headers, files=files)

            if qb_resp.status_code != 200:
                results.append({
                    "file": file_name,
                    "status": "failed",
                    "details": qb_resp.text
                })
                continue
            attachable_id = (
                qb_resp.json()
                .get("AttachableResponse", [{}])[0]
                .get("Attachable", {})
                .get("Id")
            )

            file_doc = frappe.get_doc({
                "doctype": "File",
                "file_name": file_name,
                "is_private": 0,
                "attached_to_doctype": "Invoice DB",
                "attached_to_name": invoice_id,
                "content": file_content
            })
            file_doc.insert(ignore_permissions=True)

            invoice = frappe.get_doc("Invoice DB", invoice_id)
            invoice.append("attachments", {
                "file_name": file_name,
                "file_url": file_doc.file_url,
                "label": "Uploaded Attachment",
                "quickbooks_attachable_id": attachable_id
            })            
            invoice.save(ignore_permissions=True)


            results.append({
                "file": file_name,
                "status": "success",
                "quickbooks_status_code": qb_resp.status_code,
                "frappe_file_url": file_doc.file_url
            })

        return {
            "status_code": 200,
            "Message": "File upload complete",
            "results": results
        }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "QuickBooks File Upload Error")
        return {
            "status_code": 500,
            "Message": "Something went wrong while uploading files to QuickBooks.",
            "details": str(e)
        }




@frappe.whitelist(allow_guest=True)
def get_invoice_context(docname):
    """
    Returns a structured dict from Invoice DB for use in HTML templates for invoice generation. 
    """
    doc = frappe.get_doc("Invoice DB", docname)
    customer_name = ""
    if doc.customer_id:
        customer = frappe.get_value("Customer DB", doc.customer_id, "customer_name")
        if customer:
            customer_name = customer

    structured = {
        "invoice_id": doc.name,
        "invoice_number": doc.invoice_number,
        "invoice_date": formatdate(doc.invoice_date, "mm-dd-yyyy") if doc.invoice_date else "",
        "due_date": formatdate(doc.due_date, "mm-dd-yyyy") if doc.due_date else "",
        "customer_name": customer_name or "",
        "quickbooks_customer_id": doc.quickbooks_customer_id,
        "customer_id": doc.customer_id,
        "bill_to": (doc.address or doc.bill_to or "").strip(),
        "email": doc.email_address,
        "quickbooks_customer_name": doc.quickbooks_customer_name,
        "contact": doc.contact_number,
        "bol": doc.bol,
        "origin_port": doc.orgin_port,
        "destination_port": doc.destination_port,
        "booking_id": doc.carrier_booking_number,
        "hs_code": doc.hs_code,
        "incoterm": doc.incotern,
        "comments": doc.comments or "",
        "sub_total": doc.sub_total,
        "tax": doc.tax,
        "total_amount": doc.total_amount,
        "quickbooks_invoice_link": doc.quickbooks_invoice_link,
        "quickbooks_doc_number": doc.quickbooks_doc_number,
        "shipping_date": formatdate(doc.shipping_date, "mm-dd-yyyy") if doc.shipping_date else "",
        "invoice_pdf_link": doc.invoice_pdf_link,
        "invoice_pdf_name": doc.invoice_pdf_name,
        "docket_id": doc.docket_id,
        "carrier_booking_number": doc.carrier_booking_number,
        "uom": doc.uom,
        "status": doc.status,
        "is_active": doc.is_active
    }
    structured["items"] = []
    for item in doc.items:
        structured["items"].append({
            "product_services": item.product_services or "",
            "product_description": item.product_description or "",
            "quantity": item.quantity or 0,
            "rate": item.rate or 0,
            "amount": item.amount or 0,
            "uom": getattr(item, "uom", "") or "",
            "item_id": item.item_id
        })
    attachments = []

    for attachment in doc.attachments:
        attachments.append({
            "file_name": attachment.file_name,
            "file_url": attachment.file_url,
            "label": attachment.label
        })
    structured["attachments"] = attachments

    return {
        "status": "success",
        "Message": "Invoice data fetched successfully",
        "status_code": 200,
        "data":structured
        }


@frappe.whitelist(allow_guest=True)
def generate_invoice_pdf(docname):
    """
    Regenerates the invoice PDF and replaces any existing "Generated PDF" file.
    """
    try:
        data = get_invoice_context(docname)
        if data.get("status") != "success":
            return data

        template_path = frappe.get_app_path("westside", "templates")
        env = Environment(
            loader=FileSystemLoader(template_path),
            undefined=StrictUndefined,
            trim_blocks=True,
            lstrip_blocks=True
        )
        template = env.get_template("docket_attachments/invoice.html")
        rendered_html = template.render(data=data["data"])
        pdf_data = get_pdf(rendered_html)

        file_name = f"{data['data'].get('invoice_number') or data['data'].get('invoice_id')}.pdf"

        # Get the invoice document with a fresh reload to avoid timestamp issues
        frappe.db.commit()  # Ensure all previous operations are committed
        invoice = frappe.get_doc("Invoice DB", docname)

        # Find and delete existing "Generated PDF" files
        old_files = [a for a in invoice.attachments if a.label == "Generated PDF"]
        deletion_results = []
        
        for row in old_files:
            try:
                # If QuickBooks integration exists, delete from QB first
                if hasattr(invoice, 'quickbooks_invoice_id') and invoice.quickbooks_invoice_id:
                    qb_delete_result = delete_attachment_from_quickbooks(
                        invoice.quickbooks_invoice_id, row
                    )
                    deletion_results.append({
                        "file_url": row.file_url,
                        "quickbooks_deletion": qb_delete_result
                    })

                file_docs = frappe.get_all("File", filters={"file_url": row.file_url}, fields=["name"])
                for file_doc in file_docs:
                    frappe.delete_doc("File", file_doc.name, ignore_permissions=True)

            except Exception as e:
                print(f"Failed to delete old PDF: {row.file_url}, Error: {str(e)}")

        # Remove old PDF entries from attachments
        invoice.attachments = [a for a in invoice.attachments if a.label != "Generated PDF"]

        # Create new PDF file
        file_doc = frappe.get_doc({
            "doctype": "File",
            "file_name": file_name,
            "is_private": 0,
            "attached_to_doctype": "Invoice DB",
            "attached_to_name": docname,
            "content": pdf_data
        })
        file_doc.insert(ignore_permissions=True)

        # Add new PDF attachment
        invoice.append("attachments", {
            "file_name": file_name,
            "file_url": file_doc.file_url,
            "label": "Generated PDF"
        })
        
        invoice.invoice_pdf_link = file_doc.file_url
        invoice.invoice_pdf_name = file_name
        
        # Add to update history
        invoice.append("update_history", {
            "updated_on": now_datetime(),
            "updated_by": frappe.session.user,
            "field_name": "invoice_pdf",
            "old_value": "PDF regenerated",
            "new_value": file_name,
            "quick_books_synced": 0,
            "remarks": "Invoice PDF updated with latest data"
        })

        # Save with ignore_permissions and handle timestamp conflicts
        try:
            invoice.save(ignore_permissions=True)
            frappe.db.commit()
        except frappe.TimestampMismatchError:
            # If timestamp error occurs, reload and try again
            fresh_invoice = frappe.get_doc("Invoice DB", docname)
            
            # Remove old PDF entries from fresh invoice
            fresh_invoice.attachments = [a for a in fresh_invoice.attachments if a.label != "Generated PDF"]
            
            # Add new PDF attachment to fresh invoice
            fresh_invoice.append("attachments", {
                "file_name": file_name,
                "file_url": file_doc.file_url,
                "label": "Generated PDF"
            })
            
            fresh_invoice.invoice_pdf_link = file_doc.file_url
            fresh_invoice.invoice_pdf_name = file_name
            
            fresh_invoice.append("update_history", {
                "updated_on": now_datetime(),
                "updated_by": frappe.session.user,
                "field_name": "invoice_pdf",
                "old_value": "PDF regenerated",
                "new_value": file_name,
                "quick_books_synced": 0,
                "remarks": "Invoice PDF updated with latest data"
            })
            
            fresh_invoice.save(ignore_permissions=True)
            frappe.db.commit()

        # Update docket if linked
        if invoice.docket_id:
            try:
                docket = frappe.get_doc("Docket DB", invoice.docket_id)
                if docket.docket_revisions:
                    latest_revision = sorted(
                        docket.docket_revisions,
                        key=lambda x: int(x.revision_number or 0),
                        reverse=True
                    )[0]
                    latest_revision.invoice = file_doc.file_url
                    docket.save(ignore_permissions=True)
            except Exception as e:
                print(f"Failed to update docket: {str(e)}")

        return {
            "status": "success",
            "Message": "Invoice PDF regenerated and replaced successfully.",
            "status_code": 200,
            "file_url": file_doc.file_url,
            "file_name": file_name,
            "deletion_results": deletion_results,            
        }

    except Exception as e:
        print(f"Invoice PDF Regeneration Failed: {frappe.get_traceback()}")
        return {
            "status": "error",
            "Message": f"Failed to regenerate PDF: {str(e)}",
            "status_code": 500
        }


def ensure_pdf_replacement_in_update(invoice_name):
    """
    Helper function to ensure PDF is replaced during invoice updates.
    """
    try:
        pdf_result = generate_invoice_pdf(invoice_name)
        
        if pdf_result.get("status") == "success":
            print(f"PDF successfully regenerated for invoice {invoice_name}")
        else:
            print(f"PDF regeneration failed for invoice {invoice_name}: {pdf_result.get('Message')}")
        
        return pdf_result
        
    except Exception as e:
        print(f"Error in PDF replacement: {str(e)}")
        return {
            "status": "error",
            "Message": str(e)
        }



@frappe.whitelist()
def get_all_invoices():
    """
    Returns a list of all invoices with their details.    
    Admin/System Manager: View all invoices.    
    Customer: View only invoices linked to their Customer DB entry.    
    Query Parameters:
    - search: Search in invoice_number, quickbooks_customer_name, carrier_booking_number
    - date: Filter invoices by specific date (invoice_date = date)
    - status_filter: Filter by specific status    
    - page: Page number for pagination
    - limit: Number of records per page
    """

    try:
        user = frappe.session.user
        roles = frappe.get_roles(user)

        search = frappe.form_dict.get('search', '').strip()
        date = frappe.form_dict.get('date', '').strip()
        status_filter = frappe.form_dict.get('status_filter', '').strip()

        # page = int(frappe.form_dict.get('page', 1))
        # limit = int(frappe.form_dict.get('limit', 20))
        # offset = (page - 1) * limit

        conditions = []
        params = {}
        conditions.append("is_active = 1")
        is_admin = user in ["Administrator", "Admin"] or "System Manager" in roles

        if not is_admin:
            customer_name = frappe.db.get_value("Customer DB", {"user_id": user}, "name")
            if not customer_name:
                return {
                    "status": "error",
                    "Message": "You are not permitted to access this resource.",
                    "status_code": 403
                }
            conditions.append("customer_id = %(customer_name)s")
            params["customer_name"] = customer_name

        if search:
            conditions.append("""
                (invoice_number LIKE %(search)s OR 
                 quickbooks_customer_name LIKE %(search)s OR 
                 carrier_booking_number LIKE %(search)s OR
                 quickbooks_doc_number LIKE %(search)s)
            """)
            params["search"] = f"%{search}%"

        if date:
            conditions.append("DATE(invoice_date) = %(date)s")
            params["date"] = date

        if status_filter:
            conditions.append("status = %(status_filter)s")
            params["status_filter"] = status_filter

        where_clause = f"WHERE {' AND '.join(conditions)}" if conditions else ""

        invoices_query = f"""
            SELECT 
                name,
                invoice_number,
                invoice_date,
                due_date,
                quickbooks_customer_name,
                status,
                orgin_port,
                total_amount,
                quickbooks_doc_number,
                carrier_booking_number,
                docket_id,
                is_active
            FROM `tabInvoice DB`
            {where_clause}
            ORDER BY creation DESC
        """
        invoices = frappe.db.sql(invoices_query, params, as_dict=True)

        if not invoices:
            return {
                "status": "success",
                "Message": "No invoices found for the customer.",
                "status_code": 204,
                "invoices": [],
                "counts": {
                    "total": 0,
                    "paid": 0,
                    "overdue": 0,
                    "open_invoice": 0
                },
                # "pagination": {
                #     "page": page,
                #     "limit": limit
                # }
            }

        counts_query = f"""
            SELECT 
                COUNT(*) AS total,
                SUM(CASE WHEN status = 'Paid' THEN 1 ELSE 0 END) AS paid,
                SUM(CASE WHEN status = 'Overdue' THEN 1 ELSE 0 END) AS overdue,
                SUM(CASE WHEN status = 'Open' THEN 1 ELSE 0 END) AS open_invoice
            FROM `tabInvoice DB`
            {where_clause}
        """
        counts = frappe.db.sql(counts_query, params, as_dict=True)[0]

        return {
            "status": "success",
            "Message": "Invoices fetched successfully",
            "status_code": 200,
            "invoices": invoices,
            "counts": {
                "total": int(counts.total or 0),
                "paid": int(counts.paid or 0),
                "overdue": int(counts.overdue or 0),
                "open_invoice": int(counts.open_invoice or 0)
            },
            # "pagination": {
            #     "page": page,
            #     "limit": limit,
            #     "total_pages": math.ceil(counts.total / limit) if counts.total else 0
            # }
        }


    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Get All Invoices API Error")
        return {
            "status": "error",
            "Message": "Something went wrong while fetching invoices.",
            "details": str(e),
            "status_code": 500
        }



def to_iso(date_val):
    if isinstance(date_val, (datetime, date)):
        return date_val.isoformat()
    elif isinstance(date_val, str):
        try:
            return get_datetime(date_val).isoformat()
        except Exception:
            return date_val
    return None


@frappe.whitelist()
def update_quickbooks_invoice(invoice_name, update_data):
    try:
        if isinstance(update_data, str):
            try:
                update_data = json.loads(update_data.strip().strip('"'))
            except Exception:
                return {
                    "status": "error",
                    "Message": "Invalid JSON format.",
                    "status_code": 400
                }

        if not update_data:
            return {
                "status": "error",
                "Message": "Empty update data.",
                "status_code": 400
            }

        invoice_data = update_data.get("invoice_data", {})
        updated_items = update_data.get("items", [])
        deleted_files = update_data.get("deleted_files", [])

        invoice = frappe.get_doc("Invoice DB", invoice_name)
        updated_by = frappe.session.user
        old_status = invoice.status
        original_invoice_number = invoice.invoice_number
        changes = []

        def record_change(field, old, new):
            if str(old) != str(new):
                changes.append({
                    "updated_on": now_datetime(),
                    "updated_by": updated_by,
                    "field_name": field,
                    "old_value": str(old),
                    "new_value": str(new),
                    "quick_books_synced": 0,
                    "remarks": ""
                })
                return True
            return False

        new_status = invoice_data.get("status")
        if new_status and new_status in ["Pending", "Paid", "Overdue"]:
            record_change("status", old_status, new_status)
            invoice.status = new_status
        elif invoice.due_date and get_datetime(invoice.due_date) < now_datetime() and invoice.status == "Pending":
            invoice.status = "Overdue"
            record_change("status", old_status, "Overdue")

        field_map = [
            "invoice_date", "due_date", "email_address", "contact_number",
            "comments", "sub_total", "tax", "total_amount",
            "shipping_date", "bill_to", "uom"
        ]

        for field in field_map:
            record_change(field, invoice.get(field), invoice_data.get(field))
            invoice.set(field, invoice_data.get(field))

        invoice.customer_id = update_data.get("customer_id") or invoice.customer_id
        invoice.quickbooks_customer_id = update_data.get("quickbooks_customer_id") or invoice.quickbooks_customer_id
        invoice.quickbooks_customer_name = invoice_data.get("quickbooks_customer_name") or invoice.quickbooks_customer_name
        invoice.address = invoice_data.get("address") or invoice.address

        old_item_count = len(invoice.items)
        invoice.set("items", [])
        for item_data in updated_items:
            item = {
                "product_services": item_data.get("product_services"),
                "product_description": item_data.get("product_description"),
                "quantity": item_data.get("quantity"),
                "rate": item_data.get("rate"),
                "amount": float(item_data.get("quantity") or 0) * float(item_data.get("rate") or 0),
                "item_ref_id": item_data.get("item_ref_id"),
                "uom": item_data.get("uom"),
                "item_id": item_data.get("item_ref_id")
            }
            
            if "quickbooks_line_id" in item_data:
                item["quickbooks_line_id"] = item_data["quickbooks_line_id"]
                
            invoice.append("items", item)

        if len(updated_items) != old_item_count:
            changes.append({
                "updated_on": now_datetime(),
                "updated_by": updated_by,
                "field_name": "items",
                "old_value": f"{old_item_count} items",
                "new_value": f"{len(updated_items)} items",
                "quick_books_synced": 0,
                "remarks": "Line items updated"
            })

        # Handle file deletions
        existing_files = {a.file_url: a for a in invoice.attachments}
        deletion_results = []
        for file_url in deleted_files:
            if file_url in existing_files:
                attachment_row = existing_files[file_url]
                
                qb_delete_result = delete_attachment_from_quickbooks(invoice.quickbooks_invoice_id, attachment_row)
                deletion_results.append({"file_url": file_url, "quickbooks_deletion": qb_delete_result})
                
                try:
                    attachments_to_remove = []
                    for i, attachment in enumerate(invoice.attachments):
                        if attachment.file_url == file_url:
                            attachments_to_remove.append(i)
                    
                    # Remove in reverse order to maintain indices
                    for idx in reversed(attachments_to_remove):
                        removed_attachment = invoice.attachments.pop(idx)
                    
                    # Method 2: Also try the original remove method as backup
                    for attachment in list(invoice.attachments):
                        if attachment.file_url == file_url:
                            invoice.remove(attachment)
                    
                    file_docs = frappe.get_all("File", filters={"file_url": file_url}, fields=["name"])
                    for file_doc in file_docs:
                        frappe.delete_doc("File", file_doc.name, ignore_permissions=True)
                    
                    # Record the change
                    changes.append({
                        "updated_on": now_datetime(),
                        "updated_by": updated_by,
                        "field_name": "attachments",
                        "old_value": f"Removed file: {attachment_row.file_name if hasattr(attachment_row, 'file_name') else file_url}",
                        "new_value": "File deleted",
                        "quick_books_synced": 1 if qb_delete_result.get("status") == "success" else 0,
                        "remarks": f"Attachment deleted - QB: {qb_delete_result.get('status', 'unknown')}"
                    })
                    
                except Exception as e:
                    print(f"Failed to delete file from Invoice DB: {str(e)}")  # Use print instead of frappe.log_error
                    deletion_results[-1]["invoice_db_deletion_error"] = str(e)
            else:
                deletion_results.append({
                    "file_url": file_url, 
                    "error": "File not found in invoice attachments"
                })

        # Handle file uploads
        upload_results = []
        new_files = frappe.request.files.getlist("attachments")
        if new_files:
            upload_result = upload_invoice_attachments_to_qb(invoice.quickbooks_invoice_id, new_files, invoice.name)
            upload_results.append(upload_result)
            if upload_result.get("status_code") == 200:
                changes.append({
                    "updated_on": now_datetime(),
                    "updated_by": updated_by,
                    "field_name": "attachments",
                    "old_value": "New files uploaded",
                    "new_value": f"{len(new_files)} files added",
                    "quick_books_synced": 0,
                    "remarks": f"Upload status: {upload_result.get('Message', 'Unknown')}"
                })

        for change in changes:
            invoice.append("update_history", change)

        invoice.invoice_number = original_invoice_number

        if old_status != "Paid" and invoice.status == "Paid":
            payment_result = create_quickbooks_payment(invoice)
            if payment_result.get("status") != "success":
                print(f"Payment creation failed: {payment_result.get('Message')}")  

        if old_status == "Paid" and invoice.status != "Paid":
            reversal_result = reverse_quickbooks_payment(invoice)
            if reversal_result.get("status") != "success":
                print(f"Payment reversal failed: {reversal_result.get('Message')}")  

        for row in invoice.update_history:
            if row.quick_books_synced == 0:
                row.quick_books_synced = 1

        invoice.save(ignore_permissions=True)
        frappe.db.commit()

        sync_result = sync_invoice_with_quickbooks(invoice)
        invoice = frappe.get_doc("Invoice DB", invoice.name)
        
        if sync_result.get("status") != "success":
            print(f"QuickBooks sync failed: {sync_result.get('Message')}") 

        frappe.db.commit()
        pdf_result = ensure_pdf_replacement_in_update(invoice_name)

        return {
            "status": "success",
            "status_code": 200,
            "Message": "Invoice updated and synced with QuickBooks.",
            "invoice_name": invoice.name,
            "quickbooks_invoice_id": invoice.quickbooks_invoice_id,
            "quickbooks_doc_number": invoice.quickbooks_doc_number,
            "quickbooks_link": invoice.quickbooks_invoice_link,
            "sync_result": sync_result,
            "deletion_results": deletion_results,
            "upload_results": upload_results,
            "pdf_result": pdf_result
        }

    except Exception:
        print(f"Update QuickBooks Invoice Error: {frappe.get_traceback()}")  
        return {
            "status": "error",
            "Message": "An error occurred while updating the invoice.",
            "status_code": 500
        }


def sync_invoice_with_quickbooks(invoice):
    try:
        if not invoice.quickbooks_invoice_id:
            return {
                "status": "error",
                "Message": "Missing QuickBooks Invoice ID",
                "status_code": 400
            }

        settings = frappe.get_single("QuickBooks Settings")
        access_token = frappe.cache().get_value("quickbooks_access_token")
        if not access_token:
            token_data = get_quickbooks_access_token()
            if not token_data:
                return {
                    "status": "error",
                    "Message": "Token refresh failed",
                    "status_code": 401
                }
            access_token = token_data["access_token"]

        realm_id = settings.realm_id
        minor_version = settings.minor_version or "65"
        api_url = settings.api_url or "https://sandbox-quickbooks.api.intuit.com"

        get_url = f"{api_url}/v3/company/{realm_id}/invoice/{invoice.quickbooks_invoice_id}?minorversion={minor_version}"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json"
        }

        get_resp = requests.get(get_url, headers=headers)
        if get_resp.status_code != 200:
            return {
                "status": "error",
                "Message": "Failed to fetch current invoice state",
                "status_code": get_resp.status_code
            }

        current_invoice = get_resp.json().get("Invoice", {})
        sync_token = current_invoice.get("SyncToken", "0")

        line_items = []
        for idx, item in enumerate(invoice.items, start=1):
            line_data = {
                "LineNum": idx,
                "Description": item.get("product_description") or "",
                "Amount": float(item.get("amount") or 0),
                "DetailType": "SalesItemLineDetail",
                "SalesItemLineDetail": {
                    "ItemRef": {"value": item.get("item_ref_id")},
                    "Qty": float(item.get("quantity") or 0),
                    "UnitPrice": float(item.get("rate") or 0),
                    "TaxCodeRef": {"value": "NON"}
                }
            }
            if item.get("quickbooks_line_id"):
                line_data["Id"] = item.quickbooks_line_id
            line_items.append(line_data)

        txn_date = to_iso(invoice.invoice_date) if invoice.invoice_date else None
        due_date = to_iso(invoice.due_date) if invoice.due_date else None

        payload = {
            "sparse": True,
            "Id": invoice.quickbooks_invoice_id,
            "SyncToken": sync_token,
            "CustomerRef": {"value": invoice.quickbooks_customer_id},
            "Line": line_items,
            "TxnDate": txn_date,
            "DueDate": due_date,
            "PrivateNote": invoice.comments or "",
            "ShipDate": to_iso(invoice.shipping_date) if (invoice.shipping_date) else None
        }

        update_url = f"{api_url}/v3/company/{realm_id}/invoice?minorversion={minor_version}"
        update_headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json",
            "Content-Type": "application/json"
        }

        resp = requests.post(update_url, headers=update_headers, json=payload)

        if resp.status_code != 200:
            print(f"QuickBooks Invoice Update Failed: {resp.text}")  # Use print instead of frappe.log_error
            return {
                "status": "error",
                "Message": "QuickBooks update failed",
                "status_code": resp.status_code
            }

        invoice_data = resp.json().get("Invoice", {})

        # Re-fetch the latest invoice to avoid TimestampMismatchError
        fresh_invoice = frappe.get_doc("Invoice DB", invoice.name)

        fresh_invoice.total_amount = float(invoice_data.get("TotalAmt", 0))
        fresh_invoice.tax = float(invoice_data.get("TxnTaxDetail", {}).get("TotalTax", 0))
        fresh_invoice.sub_total = sum(
            float(line.get("Amount", 0)) for line in invoice_data.get("Line", [])
            if line.get("DetailType") == "SalesItemLineDetail"
        )
        fresh_invoice.quickbooks_doc_number = invoice_data.get("DocNumber")
        fresh_invoice.quickbooks_invoice_link = f"https://app.qbo.intuit.com/app/invoice?txnId={invoice_data.get('Id')}"
        
        for i, line in enumerate(invoice_data.get("Line", [])):
            if line.get("DetailType") == "SalesItemLineDetail" and i < len(fresh_invoice.items):
                fresh_invoice.items[i].quickbooks_line_id = line.get("Id")

        # Save with QuickBooks calculated values
        fresh_invoice.save(ignore_permissions=True)
        frappe.db.commit()

        return {
            "status": "success",
            "data": invoice_data
        }

    except Exception as e:
        print(f"QuickBooks Invoice Sync Error: {frappe.get_traceback()}")  # Use print instead of frappe.log_error
        return {
            "status": "error",
            "Message": str(e),
            "status_code": 500
        }



def create_quickbooks_payment(invoice):
    """
    Creates payment in QuickBooks when invoice status changes to Paid
    """
    try:
        settings = frappe.get_single("QuickBooks Settings")
        access_token = frappe.cache().get_value("quickbooks_access_token")
        if not access_token:
            token_data = get_quickbooks_access_token()
            if not token_data:
                return {"status": "error", "Message": "Token refresh failed"}
            access_token = token_data["access_token"]

        realm_id = settings.realm_id
        minor_version = settings.minor_version or "65"
        api_url = settings.api_url or "https://sandbox-quickbooks.api.intuit.com"

        payload = {
            "Payment": {
                "TotalAmt": invoice.total_amount,
                "CustomerRef": {"value": invoice.quickbooks_customer_id},
                "Line": [{
                    "Amount": invoice.total_amount,
                    "LinkedTxn": [{
                        "TxnId": invoice.quickbooks_invoice_id,
                        "TxnType": "Invoice"
                    }]
                }]
            }
        }

        url = f"{api_url}/v3/company/{realm_id}/payment?minorversion={minor_version}"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json",
            "Content-Type": "application/json"
        }

        resp = requests.post(url, headers=headers, json=payload)

        if resp.status_code != 200:
            return {
                "status": "error",
                "Message": resp.text,
                "status_code": resp.status_code
            }

        payment_id = resp.json().get("Payment", {}).get("Id")
        if payment_id:
            frappe.db.set_value("Invoice DB", invoice.name, "quickbooks_payment_id", payment_id)

        return {
            "status": "success",
            "payment_id": payment_id
        }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "QuickBooks Payment Creation Failed")
        return {
            "status": "error",
            "Message": str(e)
        }

def reverse_quickbooks_payment(invoice):
    """
    Reverses payment when status changes from Paid to another status
    """
    try:
        if not invoice.quickbooks_payment_id:
            return {
                "status": "error",
                "Message": "No payment ID found for reversal"
            }

        settings = frappe.get_single("QuickBooks Settings")
        access_token = frappe.cache().get_value("quickbooks_access_token")
        if not access_token:
            token_data = get_quickbooks_access_token()
            if not token_data:
                return {"status": "error", "Message": "Token refresh failed"}
            access_token = token_data["access_token"]

        realm_id = settings.realm_id
        minor_version = settings.minor_version or "65"
        api_url = settings.api_url or "https://sandbox-quickbooks.api.intuit.com"

        get_url = f"{api_url}/v3/company/{realm_id}/payment/{invoice.quickbooks_payment_id}?minorversion={minor_version}"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json"
        }
        
        get_resp = requests.get(get_url, headers=headers)
        if get_resp.status_code != 200:
            return {
                "status": "error",
                "Message": "Failed to get payment details",
                "status_code": get_resp.status_code
            }
            
        payment_data = get_resp.json().get("Payment", {})
        sync_token = payment_data.get("SyncToken", "0")
        
        void_url = f"{api_url}/v3/company/{realm_id}/payment?operation=void&minorversion={minor_version}"
        void_payload = {
            "Id": invoice.quickbooks_payment_id,
            "SyncToken": sync_token
        }
        
        void_resp = requests.post(void_url, headers=headers, json=void_payload)
        
        if void_resp.status_code != 200:
            return {
                "status": "error",
                "Message": void_resp.text,
                "status_code": void_resp.status_code
            }
            
        frappe.db.set_value("Invoice DB", invoice.name, "quickbooks_payment_id", "")
        
        return {
            "status": "success",
            "Message": "Payment voided successfully"
        }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "QuickBooks Payment Reversal Failed")
        return {
            "status": "error",
            "Message": str(e)
        }
@frappe.whitelist()
def make_invoice_payment(invoice_id, payment_type="Cash", account_id=None):
	try:
		doc_invoice = frappe.get_doc("Invoice DB", {"quickbooks_invoice_id": invoice_id})
		if not doc_invoice.quickbooks_invoice_id:
			return {
				"status_code": "400",
				"message": "Invoice is not created in QuickBooks."
			}

		access_token = frappe.cache().get_value("quickbooks_access_token")
		if not access_token:
			resp_token = get_quickbooks_access_token()
			if not resp_token:
				frappe.local.response['status_code'] = "401"
				frappe.local.response['message'] = "Something went wrong in Token refresh"
			access_token = resp_token['access_token']

		headers = {
			"Authorization": f"Bearer {access_token}",
			"Content-Type": "application/json",
			"Accept": "application/json"
		}

		customer_id = json.loads(doc_invoice.quickbooks_customer).get("Id")

		# Get Bank or Credit Card account for payment
		if payment_type in ["Check", "Cash"]:
			query = "SELECT * FROM Account WHERE AccountType = 'Bank'"
		elif payment_type == "CreditCard":
			query = "SELECT * FROM Account WHERE AccountType = 'Credit Card'"
		else:
			query = "SELECT * FROM Account WHERE AccountType = 'Bank'"  # default fallback

		url = f"{QB_API_URL}/v3/company/{QB_REALM_ID}/query?query={query}&minorversion={MINOR_VERSION}"
		response = requests.get(url, headers=headers)
		dct_response = response.json()
		account_id = dct_response.get("QueryResponse", {}).get("Account", [{}])[0].get("Id") if dct_response.get("QueryResponse", {}).get("Account") else account_id

		if not account_id:
			return {
				"status_code": "400",
				"message": "No account found for the given payment type."
			}
		if not customer_id:
			return {
				"status_code": "400",
				"message": "No customer found for the given invoice."
			}

		payment_payload = {
			"CustomerRef": {
				"value": customer_id
			},
			"TotalAmt": float(doc_invoice.total_amount),
			"PrivateNote": "Invoice is paid through Frappe backend API",
			"DepositToAccountRef": {
				"value": account_id
			},
			"Line": [
				{
					"Amount": float(doc_invoice.total_amount),
					"LinkedTxn": [
						{
							"TxnId": str(doc_invoice.quickbooks_invoice_id),
							"TxnType": "Invoice"
						}
					]
				}
			]
		}

		payment_url = f"{QB_API_URL}/v3/company/{QB_REALM_ID}/payment"
		payment_response = requests.post(payment_url, headers=headers, data=json.dumps(payment_payload))

		if not payment_response.ok:
			return {
				"status_code": "400",
				"message": "Failed to mark invoice as paid in QuickBooks.",
				"data": payment_response.json().get('Fault', {}).get('Error', [{}])[0]
			}
		else:
			doc_invoice.payment_type = payment_type
			doc_invoice.quickbooks_payment_response = payment_response.text
			doc_invoice.status = "Paid"
			doc_invoice.paid_amount = doc_invoice.total_amount
			doc_invoice.balance_amount = 0
			doc_invoice.save(ignore_permissions=True)
			frappe.db.commit()
			return {
				"status_code": "200",
				"message": "Invoice marked as paid in QuickBooks successfully.",
				"data": payment_response.json()
			}

	except Exception:
		frappe.log_error(frappe.get_traceback(), "QuickBooks Invoice Payment API Error")
		frappe.throw("Something went wrong while marking the invoice as paid.")




@frappe.whitelist()
def get_quickbooks_invoice(invoice_id):
    try:
        settings = frappe.get_single("QuickBooks Settings")
        realm_id = settings.realm_id
        api_url = settings.api_url
        access_token = frappe.cache().get_value("quickbooks_access_token")
        if not access_token:
            resp_token = get_quickbooks_access_token()
            access_token = resp_token["access_token"]

        url = f"{api_url}/v3/company/{realm_id}/invoice/{invoice_id}"
        headers = {
        "Authorization": f"Bearer {access_token}",
        "Accept": "application/json"
        }

        resp = requests.get(url, headers=headers)
        if resp.status_code != 200:
            raise Exception(f"Failed to fetch invoice: {resp.text}")

        return resp.json().get("Invoice")

    except Exception:
        frappe.log_error(frappe.get_traceback(), "Fetch QB Invoice Failed")
        return None

@frappe.whitelist()
def update_quickbook_invoice_webhook(invoice_ids):

    for invoice_id in invoice_ids:
        qb_invoice = get_quickbooks_invoice(invoice_id)
        if not qb_invoice:
            continue

        doc_number = qb_invoice.get("DocNumber")
        total_amt = qb_invoice.get("TotalAmt")
        total_tax = qb_invoice.get("TxnTaxDetail", {}).get("TotalTax", 0)
        invoice_date = qb_invoice.get("TxnDate")
        due_date = qb_invoice.get("DueDate")
        private_note = qb_invoice.get("PrivateNote")
        shipping_date = qb_invoice.get("ShipDate")
        qb_lines = qb_invoice.get("Line", [])

        try:
            frappe_invoice = frappe.get_doc("Invoice DB", {"quickbooks_invoice_id": invoice_id})
        except:
            frappe.log_error(f"Invoice not found in Frappe for QB ID: {invoice_id}", "Invoice Webhook Sync")
            continue

        changes = []
        def log_change(fieldname, old, new):
            if str(old) != str(new):
                changes.append({
                    "field_name": fieldname,
                    "old_value": old,
                    "new_value": new,
                    "updated_by": "QuickBooks Webhook",
                    "updated_on": now_datetime(),
                    "quick_books_synced": 1
                })

        log_change("invoice_number", frappe_invoice.invoice_number, doc_number)
        log_change("invoice_date", frappe_invoice.invoice_date, invoice_date)
        log_change("due_date", frappe_invoice.due_date, due_date)
        log_change("total_amount", frappe_invoice.total_amount, total_amt)
        log_change("tax", frappe_invoice.tax, total_tax)
        log_change("comments", frappe_invoice.comments, private_note)
        log_change("shipping_date", frappe_invoice.shipping_date, shipping_date)

        new_items = []
        for line in qb_lines:
            if line.get("DetailType") != "SalesItemLineDetail":
                continue
            new_items.append({
                "product_services": line["SalesItemLineDetail"].get("ItemRef", {}).get("name"),
                "product_description": line.get("Description", ""),
                "quantity": line["SalesItemLineDetail"].get("Qty"),
                "rate": line["SalesItemLineDetail"].get("UnitPrice"),
                "amount": line.get("Amount")
            })

        frappe_invoice.invoice_number = doc_number
        frappe_invoice.invoice_date = invoice_date
        frappe_invoice.due_date = due_date
        frappe_invoice.total_amount = total_amt
        frappe_invoice.tax = total_tax
        frappe_invoice.comments = private_note
        frappe_invoice.shipping_date = shipping_date
        frappe_invoice.response_data = json.dumps(qb_invoice, indent=2)

        frappe_invoice.items = []  # clear
        for i in new_items:
            frappe_invoice.append("items", i)

        for c in changes:
            frappe_invoice.append("update_history", c)

        frappe_invoice.save(ignore_permissions=True)
        frappe.db.commit()
def delete_attachment_from_quickbooks(qb_invoice_id, attachment_row):
    """
    Deletes an attachment from QuickBooks and also removes it from the Frappe system.
    
    Args:
        qb_invoice_id (str): The QuickBooks invoice ID.
        attachment_row (Document): The row from the child table 'Invoice Attachments'
                                    which contains file_url and quickbooks_attachable_id.
    """
    try:
        settings = frappe.get_single("QuickBooks Settings")
        access_token = frappe.cache().get_value("quickbooks_access_token")
        if not access_token:
            token_data = get_quickbooks_access_token()
            if not token_data:
                frappe.throw("Failed to refresh QuickBooks token.")
            access_token = token_data["access_token"]

        realm_id = settings.realm_id
        api_url = settings.api_url
        minor_version = settings.minor_version

        attachable_id = getattr(attachment_row, "quickbooks_attachable_id", None)

        if not attachable_id:
            frappe.log_error("Missing attachable_id for file: " + str(attachment_row.file_url),
                             "QuickBooks Attachment Deletion Skipped")
            return {"status": "error", "message": "Missing attachable_id"}

        # First, get the current attachable to get the SyncToken
        get_url = f"{api_url}/v3/company/{realm_id}/attachable/{attachable_id}?minorversion={minor_version}"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json"
        }

        get_resp = requests.get(get_url, headers=headers)
        
        if get_resp.status_code != 200:
            frappe.log_error(f"Failed to fetch attachable {attachable_id}: {get_resp.text}", 
                           "QuickBooks Attachable Fetch Failed")
            return {"status": "error", "message": "Failed to fetch attachable"}

        attachable_data = get_resp.json().get("Attachable", {})
        sync_token = attachable_data.get("SyncToken", "0")

        # Method 1: Try using POST with operation=delete and SyncToken
        delete_payload = {
            "Id": attachable_id,
            "SyncToken": sync_token,
            "sparse": True
        }
        
        delete_url = f"{api_url}/v3/company/{realm_id}/attachable?operation=delete&minorversion={minor_version}"
        delete_headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json",
            "Content-Type": "application/json"
        }

        resp = requests.post(delete_url, headers=delete_headers, json=delete_payload)

        if resp.status_code == 200:
            frappe.logger().info(f"Successfully deleted attachment {attachable_id} from QuickBooks using POST method.")
            return {"status": "success", "message": "Attachment deleted successfully"}
        else:
            # Method 2: Fallback to DELETE request
            frappe.logger().info(f"POST delete failed, trying DELETE method for attachment {attachable_id}")
            
            delete_url_alt = f"{api_url}/v3/company/{realm_id}/attachable/{attachable_id}?minorversion={minor_version}"
            resp_alt = requests.delete(delete_url_alt, headers=headers)
            
            if resp_alt.status_code == 200:
                frappe.logger().info(f"Successfully deleted attachment {attachable_id} from QuickBooks using DELETE method.")
                return {"status": "success", "message": "Attachment deleted successfully"}
            else:
                # Method 3: Try updating the attachable to remove the entity reference
                update_payload = {
                    "Id": attachable_id,
                    "SyncToken": sync_token,
                    "AttachableRef": [],  # Remove all references
                    "sparse": True
                }
                
                update_url = f"{api_url}/v3/company/{realm_id}/attachable?minorversion={minor_version}"
                update_resp = requests.post(update_url, headers=delete_headers, json=update_payload)
                
                if update_resp.status_code == 200:
                    frappe.logger().info(f"Successfully removed references for attachment {attachable_id}")
                    return {"status": "success", "message": "Attachment references removed successfully"}
                else:
                    frappe.log_error(f"All deletion methods failed for attachment {attachable_id}. "
                                   f"POST response: {resp.text}, DELETE response: {resp_alt.text}, "
                                   f"UPDATE response: {update_resp.text}", 
                                   "QuickBooks Attachment Deletion Failed")
                    return {"status": "error", "message": "All deletion methods failed"}

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "QuickBooks Attachment Delete Error")
        return {"status": "error", "message": str(e)}



@frappe.whitelist()
def void_quickbooks_invoice(invoice_name):
    try:
        invoice = frappe.get_doc("Invoice DB", invoice_name)
        if not invoice.quickbooks_invoice_id:
            return {
                "status": "error",
                "Message": "Missing QuickBooks Invoice ID",
                "status_code": 400
            }

        # Get QuickBooks access token
        settings = frappe.get_single("QuickBooks Settings")
        access_token = frappe.cache().get_value("quickbooks_access_token")
        if not access_token:
            token_data = get_quickbooks_access_token()
            if not token_data:
                return {
                    "status": "error",
                    "Message": "Token refresh failed",
                    "status_code": 401
                }
            access_token = token_data["access_token"]

        # Get existing invoice details to fetch SyncToken
        api_url = settings.api_url
        realm_id = settings.realm_id
        minor_version = settings.minor_version
        get_url = f"{api_url}/v3/company/{realm_id}/invoice/{invoice.quickbooks_invoice_id}?minorversion={minor_version}"

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json"
        }

        get_resp = requests.get(get_url, headers=headers)
        if get_resp.status_code != 200:
            return {
                "status": "error",
                "Message": "Failed to fetch invoice for voiding",
                "details": get_resp.text,
                "status_code": get_resp.status_code
            }

        current_invoice = get_resp.json().get("Invoice", {})
        sync_token = current_invoice.get("SyncToken")

        # Void payload
        void_url = f"{api_url}/v3/company/{realm_id}/invoice?operation=update&minorversion={minor_version}"
        void_payload = {
            "Id": invoice.quickbooks_invoice_id,
            "SyncToken": sync_token,
            "sparse": True,
            "status": "Voided"
        }

        update_headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json",
            "Content-Type": "application/json"
        }

        void_resp = requests.post(void_url, headers=update_headers, json=void_payload)
        if void_resp.status_code != 200:
            frappe.log_error(void_resp.text, "QuickBooks Void Invoice Failed")
            return {
                "status": "error",
                "Message": "QuickBooks failed to void invoice",
                "status_code": void_resp.status_code
            }

        # Update Frappe
        invoice.status = "Voided"
        invoice.save(ignore_permissions=True)

        return {
            "status": "success",
            "Message": "Invoice voided successfully in QuickBooks and Frappe",
            "status_code": 200
        }

    except Exception:
        frappe.log_error(frappe.get_traceback(), "Void QuickBooks Invoice Error")
        return {
            "status": "error",
            "Message": "Error occurred while voiding invoice",
            "status_code": 500
        }
@frappe.whitelist()
def delete_quickbooks_invoice(invoice_name):
    """
    Deletes an invoice from QuickBooks and Frappe with improved error handling
    """
    try:
        invoice = frappe.get_doc("Invoice DB", invoice_name)

        if not invoice.quickbooks_invoice_id:
            return {
                "status": "error",
                "Message": "Missing QuickBooks Invoice ID",
                "status_code": 400
            }

        settings = frappe.get_single("QuickBooks Settings")
        access_token = frappe.cache().get_value("quickbooks_access_token")

        if not access_token:
            token_data = get_quickbooks_access_token()
            if not token_data:
                return {
                    "status": "error",
                    "Message": "Token refresh failed",
                    "status_code": 401
                }
            access_token = token_data["access_token"]

        api_url = settings.api_url
        realm_id = settings.realm_id
        minor_version = settings.minor_version or "75"

        # Get current invoice to fetch SyncToken
        get_url = f"{api_url}/v3/company/{realm_id}/invoice/{invoice.quickbooks_invoice_id}?minorversion={minor_version}"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json"
        }
        
        get_resp = requests.get(get_url, headers=headers)
        
        # If invoice doesn't exist in QB, proceed with Frappe deletion
        if get_resp.status_code == 404:
            frappe.logger().info(f"Invoice {invoice.quickbooks_invoice_id} not found in QuickBooks, proceeding with Frappe deletion")
        elif get_resp.status_code != 200:
            return {
                "status": "error",
                "Message": "Failed to fetch current invoice.",
                "details": get_resp.text[:200],
                "status_code": get_resp.status_code
            }
        else:
            # Delete from QuickBooks if it exists
            sync_token = get_resp.json().get("Invoice", {}).get("SyncToken")

            if sync_token:
                delete_url = f"{api_url}/v3/company/{realm_id}/invoice?operation=delete&minorversion={minor_version}"
                payload = {
                    "Id": invoice.quickbooks_invoice_id,
                    "SyncToken": sync_token
                }

                delete_headers = {
                    "Authorization": f"Bearer {access_token}",
                    "Accept": "application/json",
                    "Content-Type": "application/json"
                }

                del_resp = requests.post(delete_url, headers=delete_headers, json=payload)

                if del_resp.status_code != 200:
                    frappe.log_error(del_resp.text[:500], "QuickBooks Invoice Deletion Failed")
                    return {
                        "status": "error",
                        "Message": "QuickBooks invoice deletion failed.",
                        "details": del_resp.text[:200],
                        "status_code": del_resp.status_code
                    }

        # Delete attached files from Frappe
        deleted_files = []
        for attachment in invoice.attachments:
            try:
                file_docs = frappe.get_all("File", filters={"file_url": attachment.file_url}, fields=["name"])
                for f in file_docs:
                    frappe.delete_doc("File", f.name, ignore_permissions=True, force=True)
                    deleted_files.append(attachment.file_name)
            except Exception as file_err:
                frappe.log_error(str(file_err)[:500], f"File deletion error: {attachment.file_url}")

        # Store invoice details before deletion
        invoice_number = invoice.invoice_number
        qb_invoice_id = invoice.quickbooks_invoice_id

        # Delete the Frappe invoice
        try:
            invoice.db_set("is_active", 0, update_modified=True)
            frappe.db.commit()
        except frappe.LinkExistsError as link_err:
            return {
                "status": "error",
                "Message": "Invoice is linked to other documents and cannot be deleted.",
                "status_code": 409,
                "details": str(link_err)[:200]
            }

        return {
            "status": "success",
            "Message": f"Invoice {invoice_number} deleted successfully.",
            "quickbooks_id": qb_invoice_id,
            "invoice_name": invoice_name,
            "deleted_files": deleted_files,
            "status_code": 200
        }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Delete QuickBooks Invoice Error")
        return {
            "status": "error",
            "Message": "Unexpected error occurred during invoice deletion.",
            "details": str(e)[:200],
            "status_code": 500
        }


@frappe.whitelist(allow_guest=True)
def get_quickbooks_classes():
    try:
        settings = frappe.get_single("QuickBooks Settings")
        access_token = frappe.cache().get_value("quickbooks_access_token")

        # Refresh token if needed
        if not access_token:
            token_data = get_quickbooks_access_token()
            if not token_data:
                return {
                    "status": "error",
                    "message": "Token refresh failed",
                    "status_code": 401
                }
            access_token = token_data["access_token"]

        api_url = settings.api_url
        realm_id = settings.realm_id
        query = "SELECT Id, Name, Type, ParentRef FROM Item WHERE Type != 'Category'"
        minor_version = settings.minor_version or "75"

        endpoint = f"{api_url}/v3/company/{realm_id}/query"
        params = {
            "query": query,
            "minorversion": minor_version
        }

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json"
        }

        response = requests.get(endpoint, headers=headers, params=params)

        if response.status_code != 200:
            return {
                "status": "error",
                "message": "Failed to fetch class list",
                "status_code": response.status_code,
                "details": response.text[:500]
            }

        data = response.json()
        return {
            "status": "success",
            "classes": data.get("QueryResponse", {}).get("Class", []),
            "status_code": 200
        }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "QuickBooks Class Fetch Error")
        return {
            "status": "error",
            "message": "Unexpected error occurred",
            "details": str(e),
            "status_code": 500
        }
