<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Transboundary Movement Document</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>


<body class="p-4 font-sans text-sm">

  <h3 class="text-center font-semibold mb-4">
    FORM – 9<br>
    {SEE RULE 15(5) AND 16(5), 16(6)}<br>
    TRANSBOUNDARY MOVEMENT DOCUMENT
  </h3>

  <div class="overflow-x-auto ">
    <table class="table-auto border border-black w-full text-left">
      <thead>
        <tr class="text-center">
          <th class="border border-black px-2 py-1 w-1/12">S.NO</th>
          <th class="border border-black px-2 py-1 w-1/3">DESCRIPTION</th>
          <th class="border border-black px-2 py-1">DETAILS TO BE FURNISHED BY EXPORTER/IMPORTER</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td class="border border-black px-2 py-1 align-top">1</td>
          <td class="border border-black px-2 py-1 align-top">
            (i) Exporter (Name & Address)<br><br>
          </td>
          <td class="border border-black px-2 py-1 align-top">
            {{ doc.docket.shipper_name if doc.docket.shipper_name else ' ' }} <br>
            {{ doc.docket.shipper if doc.docket.shipper else " " }}
          </td>
        </tr>
        
         <tr>
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top">Contact Person<br><br></td>
          <td class="border border-black px-2 py-1 align-top">
          </td>
        </tr>
        
         <tr>
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top">Tel/Fax<br><br></td>
          <td class="border border-black px-2 py-1 align-top">
          </td>
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top"> (ii) Waste Generator/Exporter for** category (name & address)<br><br></td>
          <td class="border border-black px-2 py-1 align-top">
            {{ doc.docket.shipper_name if doc.docket and doc.docket.shipper_name else ' ' }} <br>
            {{ doc.docket.shipper if doc.docket.shipper else " " }}
          </td>
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top">2</td>
          <td class="border border-black px-2 py-1 align-top">Importer / Recycler (Name & Address)</td>
          <td class="border border-black px-2 py-1 align-top">
            <!-- {{ doc.docket.customer if doc.docket.customer else ' ' }}<br> -->
            {{ doc.docket.consignee if doc.docket.consignee else " "}}
          </td>
        </tr>
        
         <tr>
          <td class="border border-black px-2 py-1 align-top">3</td>
          <td class="border border-black px-2 py-1 align-top">Contact Person<br></td>
          <td class="border border-black px-2 py-1 align-top">{{ doc.docket.destination_contact if doc.docket.destination_contact else ""}}</td>
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top">4</td>
          <td class="border border-black px-2 py-1 align-top">Corresponding to applicant reference number</td>
          <td class="border border-black px-2 py-1 align-top"></td>
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top">Movement subject to single / multiple</td>
          <td class="border border-black px-2 py-1 align-top">Multiple</td> 
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top">5</td>
          <td class="border border-black px-2 py-1 align-top">Bill of Lading (attach copy)</td>
          <td class="border border-black px-2 py-1 align-top">{{ doc.docket.blno if doc.docket.blno else ' ' }}</td>
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top">6</td>
          <td class="border border-black px-2 py-1 align-top">Designation and chemical composition of the waste</td>
          <td class="border border-black px-2 py-1 align-top">No waste is generated</td>
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top">7</td>
          <td class="border border-black px-2 py-1 align-top">Physical Characteristics (3)</td>
          <td class="border border-black px-2 py-1 align-top">Solid</td>
        </tr>
        <tr>
          <td class="border border-black px-2 py-1 align-top">8</td>
          <td class="border border-black px-2 py-1 align-top">Actual quantity (Kg/Ltr/MT)</td>
          <td class="border border-black px-2 py-1 align-top">{{ doc.docket.total_qty }} KGS</td>
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top">9</td>
          <td class="border border-black px-2 py-1 align-top">Waste Identification Code</td>
          <td class="border border-black px-2 py-1 align-top">
            Schedule IX<br>
          </td>
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top">Basel No.</td>
          <td class="border border-black px-2 py-1 align-top">
            Annex IX: B3140<br>
          </td>
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top">OECD No.</td>
          <td class="border border-black px-2 py-1 align-top">
            <br>
          </td>
        </tr>
        
        
        <tr>
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top">UN No.</td>
          <td class="border border-black px-2 py-1 align-top">
          <br>
          </td>
        </tr>
        
        
        <tr>
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top">ITC (HS)</td>
          <td class="border border-black px-2 py-1 align-top">
            {{ doc.docket.hs_code if doc.docket.hs_code else ' ' }}<br>
          </td>
        </tr>
        
        
        <tr>
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top">Customs Code (HS)</td>
          <td class="border border-black px-2 py-1 align-top">
            {{ doc.docket.hs_code if doc.docket.hs_code else  ' ' }}<br>
          </td>
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top">Others (Specify)</td>
          <td class="border border-black px-2 py-1 align-top">
             <br>
          </td>
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top">10</td>
          <td class="border border-black px-2 py-1 align-top">
               OECD Classification (2)<br>
            (a) Amber/Red/other attach details<br>
            (b) Number<br>
          </td>
          <td class="border border-black px-2 py-1 align-top">
          </td>
        </tr>
        
         <tr>
          <td class="border border-black px-2 py-1 align-top">11</td>
          <td class="border border-black px-2 py-1 align-top">Packing Type (#)</td>
          <td class="border border-black px-2 py-1 align-top">
            {{doc.docket.packing_type if doc.docket.packing_type else ''}}</td>
        </tr>
        
         <tr>
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top">Number</td>
          <td class="border border-black px-2 py-1 align-top">{{ doc.docket.containers if doc.docket.containers  else "" }}</td>
        </tr>
        
         <tr>
          <td class="border border-black px-2 py-1 align-top">12</td>
          <td class="border border-black px-2 py-1 align-top">UN Classification</td>
          <td class="border border-black px-2 py-1 align-top">N.A.</td>
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top">UN Shipping Line Name</td>
          <td class="border border-black px-2 py-1 align-top"></td>
        </tr>
        <tr>
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top">UN Identification No.</td>
          <td class="border border-black px-2 py-1 align-top">4.1</td>
        </tr>
        <tr>
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top">UN Class (3)</td>
          <td class="border border-black px-2 py-1 align-top"></td>
        </tr>
        <tr>
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top">H Number (3)</td>
          <td class="border border-black px-2 py-1 align-top">H-4.1</td>
        </tr>
        <tr>
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top">Y Number</td>
          <td class="border border-black px-2 py-1 align-top"></td>
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top">13</td>
          <td class="border border-black px-2 py-1 align-top">Special handling requirements</td>
          <td class="border border-black px-2 py-1 align-top">By Crane</td>
        </tr>
      </tbody> 
      </table>

      <div style="page-break-before: always;"></div> 

      <table class="table-auto border border-black w-full text-left page-break">

        <tbody class="page-break"> 
        <tr>
          <td class="border border-black px-2 align-top w-1/12">14</td>
          <td class="align-top" colspan="2">
              
            Exporters Declaration for hazardous waste: <br/><br/>

            1, certify that the information in Sl No. 1 to 12 above is complete and correct to the best of my knowledge. I also certify that legally enforceable written contractual obligations have been entered into and are in force covering the trans boundary movement regulation / rules. <br/> <br/>
            Date: ______________________   Name: Chaitanya M Uppalapati,
            Signature:
            <img src="assets/westside/images/signature.png" alt="Signature"
                class="h-14 inline-block align-middle" />
            <br/><br/>
            
            Exporters Declaration for waste paper: NOT APPLICABLE
            
            <br/> <br/>
            
            1, certify that the information in Sl No. 1 to 12 above is complete and correct to the best of my knowledge. I also certify that the consignment does not have any hazardous waste, municipal waste or bio-medical waste. 
            <br/> <br/>
            Date :    <br/> <br/>
            Name : ________________________  Signature : _____________________
            

          </td>
        </tr>
        
        
        <tr >
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top" colspan="2">TO BE COMPLETED BY THE IMPORTER</td>
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top w-1/12">15</td>
          <td class="border border-black px-2 py-1 align-top w-1/3">Shipment received by importer/recycler</td>
          <td class="border border-black px-2 py-1 align-top"></td>
        </tr>

        <tr>
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top">Quantity Received (Kg/Ltr/MTs)</td>
          <td class="border border-black px-2 py-1 align-top"></td>
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top">Date</td>
          <td class="border border-black px-2 py-1 align-top"></td>
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top">Name</td>
          <td class="border border-black px-2 py-1 align-top">{{ doc.docket.destination_contact if doc.docket.destination_contact else ""}}</td>
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top">Signature</td>
          <td class="border border-black px-2 py-1 align-top"></td>
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top">16</td>
          <td class="border border-black px-2 py-1 align-top">
            Method of Recovery
            R-Code, if applicable
            Technology employed (attach details if necessary)
          </td>
          <td class="border border-black px-2 py-1 align-top">R3</td>
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top">17</td>
          <td class="border border-black px-2 py-1 align-top" colspan="2">
           I certify that nothing other than declared goods covered as per HW (M,H and TM) Rules is intended to be imported in the above referred consignment and will be recycled)
          </td>
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top">
            Signature
          </td>
          <td class="border border-black px-2 py-1 align-top"></td>
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top"></td>
          <td class="border border-black px-2 py-1 align-top">
            Date:
          </td>
          <td class="border border-black px-2 py-1 align-top"></td>
        </tr>
        
        <tr>
          <td class="border border-black px-2 py-1 align-top">18</td>
          <td class="border border-black px-2 py-1 align-top" colspan="2">
            Specific conditions on consenting to the movement
            (attach details, if applicable)
          </td>
        </tr>
        
      </tbody>
    </table>
    
    <div>
        <p class="text-[11px]">
             Note	: (1) Attach list, if more that one ; (2) Enter X in appropriate Box ; (3) See codes on the Reverse (X). Immediately contact Competent Authorities.; (4) more than three carriers, attach information as required in Sr.No.5 
        </p>
    </div>
    
    <p class="mt-10 font-bold">
        No war declaration 
    </p>
    <table class="table-auto border border-black w-full text-left ">
      <thead>
        <tr>
          <th class="border border-black px-2 py-1">Sr.NO</th>
          <th class="border border-black px-2 py-1">Container No.</th>
          <th class="border border-black px-2 py-1">Seal No.</th>
          <th class="border border-black px-2 py-1">Gross Wt. (MT)</th>
          <th class="border border-black px-2 py-1">Tare Wt. (MT)</th>
          <th class="border border-black px-2 py-1">Net Wt. (MT)</th>
          <th class="border border-black px-2 py-1">Packaging</th>
        </tr>
      </thead>
      <tbody>
        {% for equipment in doc.equipments %}
          <tr>
              <td class="border border-black p-2 text-center">{{ loop.index }}</td>
              <td class="border border-black p-2 text-center">{{ equipment.equipment_name if equipment.equipment_name else " " }}</td>
              <td class="border border-black p-2 text-center">{{ equipment.shipper_seal_number if equipment.shipper_seal_number else " " }}</td>
              <td class="border border-black p-2 text-center"></td>
              <td class="border border-black p-2 text-center"></td>
              <td class="border border-black p-2 text-center">
                  {{ equipment.cargo.net_weight/1000 if equipment.cargo and equipment.cargo.net_weight else '' }}
              </td>
              <td class="border border-black p-2 text-center">
                {{equipment.cargo.package_count if equipment.cargo and equipment.cargo.package_count else ' '}} &nbsp;
                  {{ equipment.cargo.package_counttype_outermost if equipment.cargo and equipment.cargo.package_counttype_outermost else '' }}
              </td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
    
    <p class="mt-5 mb-10">
        We hereby confirm that these containers does not contain any type of arms, ammunitions, mines, shells, cartridges, radioactive contaminated or otherwise as per our best knowledge
    </p>
    
    
  </div>

</body>
</html>
