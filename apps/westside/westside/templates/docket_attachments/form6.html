<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Transboundary Movement Document</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>

<body class="p-4 font-sans text-sm">

    <h3 class="text-center font-semibold mb-4">
        FORM – 6<br>
        <p class="text-xs font-normal">[See rules 13(2), 13 (10) and 14 (5)] </p><br>
        TRANSBOUNDARY MOVEMENT- MOVEMENT DOCUMENT
    </h3>

    <div class="overflow-x-auto ">
        <table class="table-auto border border-black w-full text-left">
            <thead>
                <tr class="text-center">
                    <th class="border border-black px-2 py-1 w-1/12">S.NO</th>
                    <th class="border border-black px-2 py-1 w-1/3">Description</th>
                    <th class="border border-black px-2 py-1 ">Details to be furnished by the exporter or importer </th>
                </tr>
            </thead>
            <tbody>

                <tr class="text-center">
                    <td class="border border-black px-2 py-1 align-top font-bold justify-content-center">(1)</td>
                    <td class="border border-black px-2 py-1 align-top font-bold">(2)</td>
                    <td class="border border-black px-2 py-1 align-top font-bold">(3)</td>
                </tr>


                <tr>
                    <td class="border border-black px-2 py-1 align-top">1</td>
                    <td class="border border-black px-2 py-1 align-top">
                        Exporter (Name and Address) <br />
                        Contact Person <br />
                        Tele, Fax and email
                        <br><br>
                    </td>

                    <td class="border border-black px-2 py-1 align-top">
                        {{ doc.docket.shipper_name if doc.docket.shipper_name else " "  }} <br>
                        {{ doc.docket.shipper if doc.docket.shipper else " "  }} <br>
                    </td>

                </tr>

                <tr>
                    <td class="border-t border-l border-r border-black  px-2 py-1 align-top">2</td>
                    <td class="border border-black px-1 py-1 align-top">
                        Generator(s) of the waste (Name and Address)<br />
                        Contact Person <br />
                        Tele, Fax and email <br /> <br />
                    </td>
                    <td class="border-l border-r border-black  px-2 py-1 align-top">
                    </td>
                </tr>
                <tr>
                    <td class="border-l border-r border-black  px-2 py-1 align-top"></td>
                    <td class="border border-black px-1 py-1 align-top">
                        Site of generation
                    </td>
                    <td class="border-l border-r border-black  px-2 py-1 align-top">
                    </td>
                </tr>

                <tr>
                    <td class="border border-black px-2 py-1 align-top">3</td>
                    <td class="border border-black px-2 py-1 align-top">
                        Importer or Actual user (Name and Address) <br />
                        Contact person <br />
                        Tele, Fax and email <br />
                    </td>
                    <td class="border border-black px-2 py-1 align-top font-bold">
                        <!-- {{ doc.docket.customer if doc.docket.customer else "" }}<br> -->
                        {{ doc.docket.consignee if doc.docket.consignee else "" }}<br>
                    </td>
                </tr>

                <tr>
                    <td class="border-t border-l border-r border-black  px-2 py-1 align-top">4</td>
                    <td class="border border-black px-2 py-1 align-top">Trader (Name and Address) <br />
                        Contact person <br />
                        Tele, Fax and email
                    </td>
                    <td class="border-l border-r border-black  px-2 py-1 align-top">
                        {{ doc.docket.shipper_name if doc.docket.shipper_name else " "  }}<br>
                        {{ doc.docket.shipper if doc.docket.shipper else "" }} <br>
                    </td>
                </tr>

                <tr>
                    <td class="border-l border-r border-black  px-2 py-1 align-top"></td>
                    <td class="border border-black px-2 py-1 align-top">
                        Details of actual user (Name, Address, Telephone and email)
                    </td>
                    <td class="border-l border-r border-black  px-2 py-1 align-top">

                    </td>
                </tr>

                <tr>
                    <td class="border border-black px-2 py-1 align-top">5</td>
                    <td class="border border-black px-2 py-1 align-top">
                        Corresponding to applicant Ref. No., If any
                    </td>
                    <td class="border border-black px-2 py-1 align-top">

                    </td>
                </tr>

                <tr>
                    <td class="border border-black px-2 py-1 align-top">6</td>
                    <td class="border border-black px-2 py-1 align-top">
                        Bill of lading (attach copy) 
                    </td>
                    <td class="border border-black px-2 py-1 align-top">
                        {{ doc.docket.blno if doc.docket.blno else ' ' }}
                    </td>
                </tr>

                <tr>
                    <td class="border border-black px-2 py-1 align-top">7</td>
                    <td class="border border-black px-2 py-1 align-top">
                        Country of import/export
                    </td>
                    <td class="border border-black px-2 py-1 align-top">
                            {{ doc.docket.country_of_import_export if doc.docket.country_of_import_export else ""}}
                    </td>
                </tr>

                <tr>
                    <td class="border border-black px-2 py-1 align-top">8</td>
                    <td class="border border-black px-2 py-1 align-top">
                        General description of waste
                    </td>
                    <td class="border border-black px-2 py-1 align-top">
                        {{ doc.docket.material if  doc.docket.material else ""}}
                    </td>
                </tr>

                <tr class="border-l border-black px-2 py-1 align-top">
                    <td></td>
                    <td class="border-l border-black px-2 py-1 align-top">(a) Quantity</td>
                    <td class="border-l border-r border-black px-2 py-1 align-top">{{doc.docket.total_qty}}</td>
                </tr>
                <tr>
                    <td></td>
                    <td class="border-l border-black px-2 py-1 align-top">(b) Physical characteristics</td>
                    <td class="border-l border-r border-black px-2 py-1 align-top">No waste is generated</td>
                </tr>
                <tr>
                    <td></td>
                    <td class="border-l border-black px-2 py-1 align-top">(c) Chemical composition of waste (attach details), where applicable</td>
                    <td class="border-l border-r border-black px-2 py-1 align-top">
                        
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td class="border-l border-black px-2 py-1 align-top">(d) Basel No.</td>
                    <td class="border-l border-r border-black px-2 py-1 align-top">
                        B3140 & B3080
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td class="border-l border-black px-2 py-1 align-top">(e) UN Shipping name</td>
                    <td class="border-l border-r border-black px-2 py-1 align-top">
                        {{doc.docket.carrier if doc.docket.carrier else " "}}
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td class="border-l border-black px-2 py-1 align-top">(f) UN Class</td>
                    <td class="border-l border-r border-black px-2 py-1 align-top">
                        4.1
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td class="border-l border-black px-2 py-1 align-top">(g) UN No</td>
                    <td class="border-l border-r border-black px-2 py-1 align-top">
                        H-4.1
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td class="border-l border-black px-2 py-1 align-top">(h) H Number</td>
                    <td class="border-l border-r border-black px-2 py-1 align-top"></td>
                </tr>
                <tr>
                    <td></td>
                    <td class="border-l border-black px-2 py-1 align-top">(i) Y Number</td>
                    <td class="border-l border-r border-black px-2 py-1 align-top"></td>
                </tr>
                <tr>
                    <td></td>
                    <td class="border-l border-black px-2 py-1 align-top">(j) ITC (HS)</td>
                    <td class="border-l border-r border-black px-2 py-1 align-top">{{ doc.docket.hs_code }}</td>
                </tr>
                <tr>
                    <td></td>
                    <td class="border-l border-black px-2 py-1 align-top">(k) Customs Code (H.S.)</td>
                    <td class="border-l border-r border-black px-2 py-1 align-top">{{ doc.docket.hs_code }}</td>
                </tr>
                <tr>
                    <td></td>
                    <td class="border-l border-black px-2 py-1 align-top">(l) Other (specify)</td>
                    <td class="border-l border-r border-black px-2 py-1 align-top"></td>
                </tr>

                <tr>
                    <td class="border-t border-l border-r border-black  px-2 py-1 align-top">9</td>
                    <td class="border-t border-l border-r border-black px-2 py-1 align-top">Type of packages</td>
                    <td class="border-t border-r border-black px-2 py-1 align-top">
                        {{doc.docket.packing_type if doc.docket.packing_type else ''}}
                    </td>
                </tr>
                <tr>
                    <td class="border-l border-r border-black  px-2 py-1 align-top"></td>
                    <td class="border-l border-r border-t border-black px-2 py-1 align-top">Number</td>
                    <td class="border-r border-t border-black px-2 py-1 align-top">
                        {{doc.docket.containers if doc.docket.containers else ""}}
                    </td>
                </tr>


                <tr>
                    <td class="border border-black px-2 py-1 align-top">10</td>
                    <td class="border border-black px-2 py-1 align-top">
                        Special handling requirements including emergency provision in case of accidents
                    </td>
                    <td class="border border-black px-2 py-1 align-top">
                        By Crane
                    </td>
                </tr>

            </table>

            <div style="page-break-before: always;"></div> 

            <table class="table-auto border border-black w-full text-left page-break">
              
                <tbody>
                  <tr>
                    <td class="border-t border-l border-r border-black  px-2 py-1 align-top w-1/12">11</td>
                    <td class="align-top border border-black w-1/3">
                      Movement subject to single/multiple consignment
                    </td>
                    <td class="border border-black px-2 py-1 align-top">
                      MULTIPLE
                    </td>

                <tr>
                    <td class="border-l border-r border-black  px-2 py-1 align-top"></td>
                    <td class="border border-black px-2 py-1 align-top">
                        In case of multiple movement- <br />
                        (a) Expected dates of each shipment or expected frequency of the shipments <br /><br />
                        (b) Estimated total quantity and quantities for each individual shipment
                    </td>
                    <td class="border border-black px-2 py-1 align-top">

                    </td>
                </tr>

                <tr class="text-center">
                    <td class="border border-black px-2 py-1 align-top font-bold justify-content-center">(1)</td>
                    <td class="border border-black px-2 py-1 align-top font-bold">(2)</td>
                    <td class="border border-black px-2 py-1 align-top font-bold">(3)</td>
                </tr>


                <tr>
                    <td class="border-t border-l border-r border-black  px-2 py-1 align-top">12</td>
                    <td class="border border-black px-2 py-1 align-top">
                        Transporter of waste (Name and Address)<br />
                        Contact Person <br />
                        Tele, Fax and email <br />
                    </td>
                    <td class="border border-black px-2 py-1 align-top">

                    </td>
                </tr>

              
                <tr>
                    <td class="border-l border-r border-black  px-2 py-1 align-top"></td>
                    <td class="border border-black px-2 py-1 align-top">
                        Registration number
                    </td>
                    <td class="border border-black px-2 py-1 align-top">

                    </td>
                </tr>

              
                <tr>
                    <td class="border-l border-r border-black  px-2 py-1 align-top"></td>
                    <td class="border border-black px-2 py-1 align-top">
                        Means of transport (road, rail, inland waterway, sea, air)
                    </td>
                    <td class="border border-black px-2 py-1 align-top"></td>
                </tr>
                <tr>
                    <td class="border-l border-r border-black  px-2 py-1 align-top"></td>
                    <td class="border border-black px-2 py-1 align-top">
                        Date of Transfer
                    </td>
                    <td class="border border-black px-2 py-1 align-top"></td>
                </tr>

                <tr>
                    <td class="border-l border-r border-black  px-2 py-1 align-top"></td>
                    <td class="border border-black px-2 py-1 align-top">
                        Signature of Carrier’s representative
                    </td>
                    <td class="border border-black px-2 py-1 align-top"></td>
                </tr>

                <tr>
                    <td class="border border-black px-2 py-1 align-top">13</td>
                    <td class="px-2 py-1 align-top font-bold" colspan="21">
                        Exporters Declaration for hazardous and other waste: <br /><br />
                        <p class="font-normal">
                            I certify that the information in Sl. Nos. 1 to 12 above are complete and correct to my best knowledge. I also certify that legally-enforceable written contractual obligations have been entered into and are in force covering the transboundary movement regulations/rules.
                            <br />
                            <br />
                            Date: ______{{ doc.current_date }}______________
                            <br /><br />
                            <div class="relative mt-6 inline-block">
                                <span class="text-sm">Signature : __________</span>
                              
                                <img src="assets/westside/images/signature.png" alt="Signature"
                                     class="w-36 h-16 opacity-80 absolute mx-36 left-[100px] -top-8"
                                     style="transform: rotate(-5deg);" />
                              </div>
                      </span>
                            <br /> <br />
                            Name: ______CHAITANYA M UPPALAPATI________
                        </p>
                    </td>
                </tr>

                <tr>
                    <td class="px-2 py-1 align-top"></td>
                    <td class="border-t border-r border-black px-2 py-1 align-top font-bold" colspan="2">
                        <br />
                        TO BE COMPLETED BY IMPORTER (ACTUAL USER OR TRADER)
                        <br /><br />
                    </td>
                </tr>


                <tr class="border-l border-t border-black px-2 py-1 align-top">
                    <td class="px-2 py-1 align-top">14</td>
                    <td class="border-l border-r border-b border-black px-2 py-1 align-top" colspan="2">
                        Shipment received by importer/ actual user/trader
                    </td>

                </tr>
                <tr>
                    <td></td>
                    <td class="border-l border-r border-black px-2 py-1 align-top" colspan="2">
                        Quantity : <br /><br />
                        Received : _________________________Kg/litres <br /><br />
                        Date: _____________________________<br /><br />
                        Name: ____________________________<br /><br />
                        Signature: _________________________<br /><br />
                    </td>

                </tr>



                <tr>
                    <td class="border-t border-black px-2 py-1 align-top">15</td>
                    <td class="border border-black px-2 py-1 align-top">
                        Methods of recovery
                    </td>
                    <td class="border border-black px-2 py-1 align-top">

                    </td>
                </tr>

                <tr>
                    <td class="border-l border-r border-black  px-2 py-1 align-top"></td>
                    <td class="border border-black px-2 py-1 align-top">
                        R code*
                    </td>
                    <td class="border border-black px-2 py-1 align-top"></td>
                </tr>

                <tr>
                    <td class="border-l border-r border-black  px-2 py-1 align-top"></td>
                    <td class="border border-black px-2 py-1 align-top">
                        Technology employed (Attached details if necessary)
                    </td>
                    <td class="border border-black px-2 py-1 align-top"></td>
                </tr>

            </table>

            <div style="page-break-before: always;"></div> 
    
            <table class="table-auto border border-black w-full text-left page-break">
            
                <tbody>

                <tr>
                    <td class="border border-black px-2 py-1 align-top w-1/12">16</td>
                    <td class="border border-black px-2 py-1 align-top" colspan="2">
                        I certify that nothing other than declared goods covered as per these rules is intended to be imported in the above referred consignment and will be recycled /utilized. <br /><br />
                        Signature: _______________________<br /><br />
                        Date: ____________________________
                    </td>
                </tr>
                
                <tr>
                    <td class="border-t border-black px-2 py-1 align-top">17</td>
                    <td class="border border-black px-2 py-1 align-top">
                        SPECIFIC CONDITIONS ON CONSENTING TO THE MOVEMENT if applicable.
                    </td>
                    <td class="border border-black px-2 py-1 align-top">
                        (attach details)
                    </td>
                </tr>

                <tr>
                    <td class="border border-black px-2 py-1 align-top font-bold" colspan="3">
                        Notes:-
                        <p class="font-normal">(1) Attach list, if more than one; (2) Select appropriate option; (3) Immediately contact competent authority in case of any emergency; (4) If more than one transporter carriers, attach information as required in SL. No. 12.
                        </p>
                    </td>
                </tr>
            </tbody>
        </table>
        <br><br><br>
        <div>
            <p class="text-[14px] font-bold text-center mt-10">
                List of abbreviations used in the Movement Document
            </p>
            <p class="text-[14px]  text-start mt-5 mx-10">
                <strong>Recovery Operations (*)</strong> <br />
                <strong>R1</strong> &nbsp;&nbsp;
                Use as a fuel (other than in direct incineration) or other means to generate energy. <br />
                <strong>R2</strong> &nbsp;&nbsp;
                Solvent reclamation/regeneration.
            </p>
        </div>
    
        <script>
            document.addEventListener("DOMContentLoaded", function () {
              const today = new Date();
              const formattedDate = today.toLocaleDateString("en-IN"); // DD/MM/YYYY
              document.getElementById("current-date").textContent = formattedDate;
            });
        </script>
</body>

</html>