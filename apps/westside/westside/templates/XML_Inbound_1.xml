<?xml version="1.0" encoding="ISO-8859-1"?>
<Message>
    <Header>
		<MessageType MessageVersion="{{ doc_version }}">ShippingInstruction</MessageType>
		<DocumentIdentifier>{{ shipment_id }}</DocumentIdentifier>
		<DateTime DateType="Document">{{ date_type }}</DateTime>
		<Parties>
			<PartnerInformation PartnerRole="Sender">
				<PartnerIdentifier Agency="AssignedBySender">WESELL</PartnerIdentifier>
				<PartnerName>Westside Exports LLC</PartnerName>
				<ContactInformation>
					<ContactName ContactType="Informational">Westside Exports LLC</ContactName>
					<CommunicationValue CommunicationType="Telephone">************</CommunicationValue>
                    {% if sender_email_id %}
					<CommunicationValue CommunicationType="Email">{{ sender_email_id }}</CommunicationValue>
                    {% endif %}
				</ContactInformation>
			</PartnerInformation>
			<PartnerInformation PartnerRole="Recipient">
				<PartnerIdentifier Agency="AssignedByRecipient">INTTRA</PartnerIdentifier>
			</PartnerInformation>
		</Parties>
	</Header>
    <MessageBody>
        <MessageProperties>
            <ShipmentID>
                <ShipmentIdentifier MessageStatus="{{ message_status }}">{{ shipment_identifier }}</ShipmentIdentifier>
                <DocumentVersion>{{doc_version}}</DocumentVersion>
            </ShipmentID>
            <DateTime DateType="Message">{{ date_time }}</DateTime>
            {% for charge in freight_charge_categories %}
            <ChargeCategory PrepaidorCollectIndicator="{{ charge.prepaid_or_collect }}" ChargeType="{{ charge.charge_type }}"/>
            {% endfor %}
            {% if shippers__declared_value and currency_type %}
            <ShipmentDeclaredValue Currency="{{ currency_type.alphabetic_code }}">{{ shippers__declared_value }}</ShipmentDeclaredValue>    
            {% endif %}

            {% if letter_of_credit_reference %}
            <LetterOfCreditDetails>
                {% if letter_of_credit_reference %}
                <LetterOfCreditNumber>{{ letter_of_credit_reference }}</LetterOfCreditNumber>
                {% endif %}
                {% if lcr_issue_date %}
                <DateTime DateType="IssueDate">{{ lcr_issue_date }}</DateTime>
                {% endif %}
                {% if lcr_expiry_date %}
                <DateTime DateType="ExpiryDate">{{ lcr_expiry_date }}</DateTime>
                {% endif %}
            </LetterOfCreditDetails>
            {% endif %}

            {% if export_license_number %}
            <ExportLicenseDetails>
                {% if export_license_number %}
                <ExportLicenseNumber>{{ export_license_number }}</ExportLicenseNumber>
                {% endif %}
                {% if eln_issue_date %}
                <DateTime DateType="IssueDate">{{ eln_issue_date }}</DateTime>
                {% endif %}
                {% if eln_expiry_date %}
                <DateTime DateType="ExpiryDate">{{ eln_expiry_date }}</DateTime>
                {% endif %}
            </ExportLicenseDetails>
            {% endif %}         

            {% if location_code and location_name %}
            <BlLocations>
             {% if location_code and location_name %}
                <Location LocationType="FreightPaymentLocation">
                    {% if location_code %}
                    <LocationCode Agency="UN">{{ location_code }}</LocationCode>
                    {% endif %}
                    {% if location_name %}
                    <LocationName>{{ location_name }}</LocationName>
                    {% endif %}
                </Location>
                {% endif %}
                {% if bl_release_code or bl_release_name %}
                <Location LocationType="BillOfLadingRelease">
                    {% if bl_release_code %}
                    <LocationCode Agency="UN">{{ bl_release_code }}</LocationCode>
                    {% endif %}
                    {% if bl_release_name %}
                    <LocationName>{{ bl_release_name }}</LocationName>
                    {% endif %}
                    {% if requested_date_of_issue %}
                    <DateTime DateType="BlReleaseDate">{{ requested_date_of_issue }}</DateTime>
                    {% endif %}
                </Location>                
                {% endif %}
            </BlLocations>
            {% endif %}              

            {% if carrier_booking_number %}
            <ReferenceInformation ReferenceType="BookingNumber">{{ carrier_booking_number }}</ReferenceInformation>
            {% endif %}
            {% if shipper_identifying_number %}
            <ReferenceInformation ReferenceType="ShipperIdentifyingNumber">{{ shipper_identifying_number }}</ReferenceInformation>
            {% endif %}
            {% if bill_of_lading_number %}
            <ReferenceInformation ReferenceType="BillOfLadingNumber">{{ bill_of_lading_number }}</ReferenceInformation>
            {% endif %}
            {% if freight_forwarder_reference %}
            <ReferenceInformation ReferenceType="FreightForwarderReference">{{ freight_forwarder_reference }}</ReferenceInformation>
            {% endif %}
            {% if transaction_number %}
            <ReferenceInformation ReferenceType="TransactionReferenceNumber">{{ transaction_number }}</ReferenceInformation>
            {% endif %}
            {% if unique_consignment_reference %}
            <ReferenceInformation ReferenceType="UniqueConsignmentReference">{{ unique_consignment_reference }}</ReferenceInformation>
            {% endif %}
            {% if purchase_order_number %}
            <ReferenceInformation ReferenceType="PurchaseOrderNumber">{{ purchase_order_number }}</ReferenceInformation>
            {% endif %}
            {% if customs_house_broker_reference %}
            <ReferenceInformation ReferenceType="BrokerReferenceNumber">{{customs_house_broker_reference}}</ReferenceInformation>
            {% endif %}
            {% if contract_reference_number %}
            <ReferenceInformation ReferenceType="ContractNumber">{{ contract_reference_number }}</ReferenceInformation>
            {% endif %}
            {% if exporter_reference_number %}
            <ReferenceInformation ReferenceType="ExportersReferenceNumber">{{exporter_reference_number}}</ReferenceInformation>
            {% endif %}
            {% if consignee_order_number %}
            <ReferenceInformation ReferenceType="ConsigneeOrderNumber">{{ consignee_order_number }}</ReferenceInformation>
            {% endif %}
            {% if invoice_reference_number %}
            <ReferenceInformation ReferenceType="InvoiceNumber">{{ invoice_reference_number }}</ReferenceInformation>
            {% endif %}
            {% if government_reference_or_fmc_number %}
            <ReferenceInformation ReferenceType="FederalMaritimeComNumber">{{ government_reference_or_fmc_number }}</ReferenceInformation>
            {% endif %}            
            {% if pcin_number_1 %}
            <ReferenceInformation ReferenceType="PCINNumber">{{ pcin_number_1 }}</ReferenceInformation>
            {% endif %}
            {% if pcin_number_2 %}
            <ReferenceInformation ReferenceType="PCINNumber">{{ pcin_number_2 }}</ReferenceInformation>
            {% endif %}
            {% if pcin_number_3 %}
            <ReferenceInformation ReferenceType="PCINNumber">{{ pcin_number_3 }}</ReferenceInformation>
            {% endif %}
            {% if csn_number_1 %}
            <ReferenceInformation ReferenceType="CSNNumber">{{ csn_number_1 }}</ReferenceInformation>
            {% endif %}
            {% if csn_number_2 %}
            <ReferenceInformation ReferenceType="CSNNumber">{{ csn_number_2 }}</ReferenceInformation>
            {% endif %}
            {% if csn_number_3 %}
            <ReferenceInformation ReferenceType="CSNNumber">{{ csn_number_3 }}</ReferenceInformation>
            {% endif %}
            {% if mcin_number_1 %}
            <ReferenceInformation ReferenceType="MCINNumber">{{ mcin_number_1 }}</ReferenceInformation>
            {% endif %}
            {% if mcin_number_2 %}
            <ReferenceInformation ReferenceType="MCINNumber">{{ mcin_number_2 }}</ReferenceInformation>
            {% endif %}
            {% if mcin_number_3 %}
            <ReferenceInformation ReferenceType="MCINNumber">{{ mcin_number_3 }}</ReferenceInformation>
            {% endif %}          
            {% if documentation_clauses or shipment_comments or equipment %}
            <Instructions>
                {% if documentation_clauses %}
                <ShipmentComments CommentType="General">{{ documentation_clauses }}</ShipmentComments> 
                {% endif %}

                {% if shipment_comments %}
                    {% for comment in shipment_comments %}
                    <ShipmentComments CommentType="General">{{ comment }}</ShipmentComments> 
                    {% endfor %}
                {% endif %}

                {% for data in equipment %}
                    {% if data.wood_declaration %}
                    <ShipmentComments CommentType="BlClause">{{ data.wood_declaration }}</ShipmentComments> 
                    {% endif %}
                {% endfor %}
            </Instructions>
            {% endif %}

            {% if total_number_of_containers %}
            <ControlTotal>
                <NumberOfEquipment>{{ total_number_of_containers }}</NumberOfEquipment>
                <NumberOfPackages>{{ total_number_of_packages }}</NumberOfPackages>
                <GrossWeight UOM="KGM">{{ total_shipment_weight }}</GrossWeight>
                <GrossVolume UOM="MTQ">{{ total_shipment_volume }}</GrossVolume>
            </ControlTotal>
            {% endif %}
            <HaulageDetails MovementType="{{ move_type }}" ServiceType="{{ shipment_type }}"/>
            <TransportationDetails TransportStage="Main" TransportMode="{{ transport_mode }}">
                <ConveyanceInformation>
                    <ConveyanceName>{{ conveyance_name }}</ConveyanceName>
                    <VoyageTripNumber>{{ voyage }}</VoyageTripNumber>
                    {% for role, partner in partners.items() %}
                    {% if role == 'carrier' and partner.id %}
                    <CarrierSCAC>{{ partner.id }}</CarrierSCAC>
                    {% endif %}
                    {% endfor %}
                    {% if lloyds_code %}
                    <TransportIdentification TransportIdentificationType="LloydsCode">{{ lloyds_code }}</TransportIdentification>
                    {% endif %}
                </ConveyanceInformation>
                {% if place_receipt_code or place_receipt_name %}
                <Location LocationType="PlaceOfReceipt">
					{% if place_receipt_code %}
                    <LocationCode Agency="UN">{{ place_receipt_code }}</LocationCode>
                    {% endif %}
					{% if place_receipt_name %}
                    <LocationName>{{ place_receipt_name }}</LocationName>
                    {% endif %}
					{% if place_receipt_country %}
                    <LocationCountry>{{ place_receipt_country }}</LocationCountry>
                    {% endif %}
				</Location>
                {% endif %}                
                {% if port_loading_code or port_loading_name %}
				<Location LocationType="PortOfLoading">
					{% if port_loading_code %}
                    <LocationCode Agency="UN">{{ port_loading_code }}</LocationCode>
                    {% endif %}
					{% if port_loading_name %}
                    <LocationName>{{ port_loading_name }}</LocationName>
                    {% endif %}
					{% if port_loading_country %}
                    <LocationCountry>{{ port_loading_country }}</LocationCountry>
                    {% endif %}
				</Location>
                {% endif %}				
                {% if port_discharge_code or port_discharge_name %}
				<Location LocationType="PortOfDischarge">
					{% if port_discharge_code %}
                    <LocationCode Agency="UN">{{ port_discharge_code }}</LocationCode>
                    {% endif %}
					{% if port_discharge_name %}
                    <LocationName>{{ port_discharge_name }}</LocationName>
                    {% endif %}
					{% if port_discharge_country %}
                    <LocationCountry>{{ port_discharge_country }}</LocationCountry>
                    {% endif %}
				</Location>
                {% endif %}                
                {% if place_of_delivery_code or place_of_delivery_name %}
                <Location LocationType="PlaceOfDelivery">
					{% if place_of_delivery_code %}
                    <LocationCode Agency="UN">{{ place_of_delivery_code }}</LocationCode>
                    {% endif %}
					{% if place_of_delivery_name %}
                    <LocationName>{{ place_of_delivery_name }}</LocationName>
                    {% endif %}
					{% if place_of_delivery_country %}
                    <LocationCountry>{{ place_of_delivery_country }}</LocationCountry>
                    {% endif %}
				</Location>
                {% endif %}
            </TransportationDetails>
            <Parties>            
            {% if si_requestor %}
                <PartnerInformation PartnerRole="BLNotifyParty">
                    <ContactInformation>
                        <ContactName ContactType="BLAutoShare">{{ si_requestor.contact_name }}</ContactName>
                        <WebBLDocuments BLDocType="DraftUnrated"/>
                        {% for email in si_requestor.emails %}
                            <CommunicationValue CommunicationType="Email">{{ email }}</CommunicationValue>
                        {% endfor %}
                    </ContactInformation>
                </PartnerInformation>
            {% endif %} 
                {% for data in shipper %}
                {% if data.shipper_name %}
                    <PartnerInformation PartnerRole="Shipper">
                        {% if data.shipper_code %}
                            <PartnerIdentifier Agency="AssignedBySender">{{ data.shipper_code }}</PartnerIdentifier>
                        {% endif %}
                        <PartnerName>{{ data.shipper_name }}</PartnerName>
                        <ContactInformation>
                            {% if data.contact_name %}
                                <ContactName ContactType="Informational">{{ data.contact_name }}</ContactName>
                            {% endif %}
                            {% if data.fax %}
                                <CommunicationValue CommunicationType="Fax">{{ data.fax }}</CommunicationValue>
                            {% endif %}
                            {% if data.email %}
                                <CommunicationValue CommunicationType="Email">{{ data.email }}</CommunicationValue>
                            {% endif %}
                            {% if data.phone %}
                                <CommunicationValue CommunicationType="Telephone">{{ data.phone }}</CommunicationValue>
                            {% endif %}
                        </ContactInformation>
                        {% if data.address_lines %}
                        {% set address_lines = data.address_lines %}
                        <AddressInformation>
                                {% if address_lines[0:35]%}                           
                                <AddressLine>{{ address_lines[0:35] }}</AddressLine>
                                {% endif %}
                                {% if address_lines[35:70]%} 
                                <AddressLine>{{ address_lines[35:70] }}</AddressLine>
                                {% endif %}
                                {% if address_lines[70:105]%} 
                                <AddressLine>{{ address_lines[70:105] }}</AddressLine>
                                {% endif %}
                                {% if address_lines[105:140]%} 
                                <AddressLine>{{ address_lines[105:140] }}</AddressLine> 
                                {% endif %}                           
                        </AddressInformation>
                        {% endif %}
                        {% if shipper_identifying_number %}
                        <PartyReferenceInformation ReferenceType="GovtReferenceNumber">{{ shipper_identifying_number }}</PartyReferenceInformation>
                        {% endif %}
                    </PartnerInformation>
                {% endif %}
                {% endfor %}
                {% if customer %}                
                {% for item in customer %}
            <PartnerInformation PartnerRole="Consignee">
                <PartnerName>{{ item.customer_name }}</PartnerName>
                {% if item.contact or item.fax or item.email_id or item.phone %}
                <ContactInformation>
                    {% if item.contact %}
                    <ContactName ContactType="Informational">{{ item.contact }}</ContactName>
                    {% endif %}
                    {% if item.fax %}
                    <CommunicationValue CommunicationType="Fax">{{ item.fax }}</CommunicationValue>
                    {% endif %}
                    {% if item.email_id %}
                    <CommunicationValue CommunicationType="Email">{{ item.email_id }}</CommunicationValue>
                    {% endif %}
                    {% if item.phone %}
                    <CommunicationValue CommunicationType="Telephone">{{ item.phone }}</CommunicationValue>
                    {% endif %}
                </ContactInformation>
                {% endif %}
                {% if item.address_lines %}
                {% set addr = item.address_lines %}
                <AddressInformation>
                    {% if addr[0:35] %}
                    <AddressLine>{{ addr[0:35] }}</AddressLine>
                    {% endif %}
                    {% if addr[35:70] %}
                    <AddressLine>{{ addr[35:70] }}</AddressLine>
                    {% endif %}
                    {% if addr[70:105] %}
                    <AddressLine>{{ addr[70:105] }}</AddressLine>
                    {% endif %}
                    {% if addr[105:140] %}
                    <AddressLine>{{ addr[105:140] }}</AddressLine>
                    {% endif %}
                    {% if addr[140:175] %}
                    <AddressLine>{{ addr[140:175] }}</AddressLine>
                    {% endif %}
                </AddressInformation>
                {% endif %}
                {% if consignee_identifying_number %}
                <PartyReferenceInformation ReferenceType="GovtReferenceNumber">{{ consignee_identifying_number }}</PartyReferenceInformation>
                {% endif %}
            </PartnerInformation>
                {% endfor %}

            {% elif si_customer_name_entry and si_customer_address_entry %}                
            <PartnerInformation PartnerRole="Consignee">
                <PartnerName>{{ si_customer_name_entry }}</PartnerName>
                {% if si_customer_address_entry %}
                <AddressInformation>                    
                    {% set address_lines = si_customer_address_entry %}
                    {% if address_lines[0:35] %}
                    <AddressLine>{{ address_lines[0:35] }}</AddressLine>
                    {% endif %}
                    {% if address_lines[35:70] %}
                    <AddressLine>{{ address_lines[35:70] }}</AddressLine>
                    {% endif %}
                    {% if address_lines[70:105] %}
                    <AddressLine>{{ address_lines[70:105] }}</AddressLine>
                    {% endif %}
                    {% if address_lines[105:140] %}
                    <AddressLine>{{ address_lines[105:140] }}</AddressLine>
                    {% endif %}
                </AddressInformation>
                {% endif %}
                {% if consignee_identifying_number %}
                <PartyReferenceInformation ReferenceType="GovtReferenceNumber">{{ consignee_identifying_number }}</PartyReferenceInformation>
                {% endif %}
            </PartnerInformation>
            {% endif %}

            {% if notify_party %}
                {% for data in notify_party %}
                {% if data.name %}
                <PartnerInformation PartnerRole="NotifyParty">
                    <PartnerName>{{ data.name }}</PartnerName>
                    <ContactInformation>
                        {% if data.contact_name %}
                        <ContactName ContactType="Informational">{{ data.contact_name }}</ContactName>
                        {% endif %}
                        {% if data.fax %}
                        <CommunicationValue CommunicationType="Fax">{{ data.fax }}</CommunicationValue>
                        {% endif %}
                        {% if data.email %}
                        <CommunicationValue CommunicationType="Email">{{ data.email }}</CommunicationValue>
                        {% endif %}
                        {% if data.phone %}
                        <CommunicationValue CommunicationType="Telephone">{{ data.phone }}</CommunicationValue>
                        {% endif %}
                    </ContactInformation>
                    {% if data.address_lines %}
                    {% set address_lines = data.address_lines %}
                    <AddressInformation>
                    {% if address_lines [0:35] %}                        
                        <AddressLine>{{ address_lines[0:35] }}</AddressLine>
                        {% endif %}
                        {% if address_lines [35:70] %}
                        <AddressLine>{{ address_lines[35:70] }}</AddressLine>
                        {% endif %}
                        {% if address_lines [70:105] %}
                        <AddressLine>{{ address_lines[70:105] }}</AddressLine>
                        {% endif %}
                        {% if address_lines [105:140] %}
                        <AddressLine>{{ address_lines[105:140] }}</AddressLine>
                        {% endif %}
                        {% if address_lines [140:175] %}
                        <AddressLine>{{ address_lines[140:175] }}</AddressLine> 
                        {% endif %}                       
                    </AddressInformation>
                    {% endif %}
                    {% if notify_party_pan_number %}
                    <PartyReferenceInformation ReferenceType="GovtReferenceNumber">{{ notify_party_pan_number }}</PartyReferenceInformation>
                    {% endif %}
                </PartnerInformation>
                {% endif %}
                {% endfor %}
            {% elif si_notify_party_name_entry and si_notify_party_address_entry %}
                <PartnerInformation PartnerRole="NotifyParty">
                    <PartnerName>{{ si_notify_party_name_entry }}</PartnerName>
                    {% if si_notify_party_address_entry %}
                    <AddressInformation>
                        {% set address_lines = si_notify_party_address_entry %}
                        {% if address_lines[0:35] %}
                        <AddressLine>{{ address_lines[0:35] }}</AddressLine>
                        {% endif %}
                        {% if address_lines[35:70] %}
                        <AddressLine>{{ address_lines[35:70] }}</AddressLine>
                        {% endif %}
                        {% if address_lines[70:105] %}
                        <AddressLine>{{ address_lines[70:105] }}</AddressLine>
                        {% endif %}
                        {% if address_lines[105:140] %}
                        <AddressLine>{{ address_lines[105:140] }}</AddressLine>
                        {% endif %}
                    </AddressInformation>
                    {% endif %}
                    {% if notify_party_pan_number %}
                    <PartyReferenceInformation ReferenceType="GovtReferenceNumber">{{ notify_party_pan_number }}</PartyReferenceInformation>
                    {% endif %}
                </PartnerInformation>
            {% endif %}

                <PartnerInformation PartnerRole="Requestor">
                    <PartnerIdentifier Agency="AssignedBySender">WESELL</PartnerIdentifier>
                    <PartnerName>Westside Exports LLC</PartnerName>
                    <ContactInformation>
                        <ContactName ContactType="Informational">Westside Exports LLC</ContactName>
                        <CommunicationValue CommunicationType="Telephone">************</CommunicationValue>
                        {% if sender_email_id %}
                        <CommunicationValue CommunicationType="Email">{{ sender_email_id }}</CommunicationValue>
                        {% endif %}
                        </ContactInformation>  
                        {% if bill_type == "Seaway/Express" %}
                    {% if unfreightedno_of_documents %}
                    <DocumentationRequirements>
                        <Documents DocumentType="BillOfLadingOriginal" Freighted="False"/>
                        <Quantity>{{ unfreightedno_of_documents }}</Quantity>
                    </DocumentationRequirements>
                    {% endif %}

                    {% if freightedno_of_documents %}
                    <DocumentationRequirements>
                        <Documents DocumentType="BillOfLadingOriginal" Freighted="True"/>
                        <Quantity>{{ freightedno_of_documents }}</Quantity>
                    </DocumentationRequirements>
                    {% endif %}

                {% elif bill_type == "Original" %}
                    {% if unfreightedno_of_documents %}
                    <DocumentationRequirements>
                        <Documents DocumentType="BillOfLadingOriginal" Freighted="False"/>
                        <Quantity>{{ unfreightedno_of_documents }}</Quantity>
                    </DocumentationRequirements>
                    {% endif %}

                    {% if freightedno_of_documents %}
                    <DocumentationRequirements>
                        <Documents DocumentType="BillOfLadingOriginal" Freighted="True"/>
                        <Quantity>{{ freightedno_of_documents }}</Quantity>
                    </DocumentationRequirements>
                    {% endif %}

                    {% if non_negotiable_unfreighted_no_of_copies %}
                    <DocumentationRequirements>
                        <Documents DocumentType="BillOfLadingCopy" Freighted="False"/>
                        <Quantity>{{ non_negotiable_unfreighted_no_of_copies }}</Quantity>
                    </DocumentationRequirements>
                    {% endif %}

                    {% if non_negotiable_freightedno_of_copies %}
                    <DocumentationRequirements>
                        <Documents DocumentType="BillOfLadingCopy" Freighted="True"/>
                        <Quantity>{{ non_negotiable_freightedno_of_copies }}</Quantity>
                    </DocumentationRequirements>
                    {% endif %}
                {% endif %}
                </PartnerInformation>
                {% if additional_notify_party_1 %}
                {% for data in additional_notify_party_1 %}
                {% if data.name %}
                <PartnerInformation PartnerRole="NotifyParty1">
                    <PartnerName>{{ data.name }}</PartnerName>
                    <ContactInformation>
                        {% if data.contact_name %}
                        <ContactName ContactType="Informational">{{ data.contact_name }}</ContactName>
                        {% endif %}
                        {% if data.fax %}
                        <CommunicationValue CommunicationType="Fax">{{ data.fax }}</CommunicationValue>
                        {% endif %}
                        {% if data.email %}
                        <CommunicationValue CommunicationType="Email">{{ data.email }}</CommunicationValue>
                        {% endif %}
                        {% if data.phone %}
                        <CommunicationValue CommunicationType="Telephone">{{ data.phone }}</CommunicationValue>
                        {% endif %}
                    </ContactInformation>
                    {% if data.address_lines %}
                    {% set address_lines = data.address_lines %}
                    <AddressInformation>
                        {% if address_lines[0:35] %}
                        <AddressLine>{{ address_lines[0:35] }}</AddressLine>
                        {% endif %}
                        {% if address_lines[35:70] %}
                        <AddressLine>{{ address_lines[35:70] }}</AddressLine>
                        {% endif %}
                        {% if address_lines[70:105] %}
                        <AddressLine>{{ address_lines[70:105] }}</AddressLine>
                        {% endif %}
                        {% if address_lines[105:140] %}
                        <AddressLine>{{ address_lines[105:140] }}</AddressLine>
                        {% endif %}
                    </AddressInformation>
                    {% endif %}
                </PartnerInformation>
                {% endif %}
                {% endfor %}
            {% elif manual_additional_notify_party_1 %}
                {% for data in manual_additional_notify_party_1 %}
                {% if data.name %}
                <PartnerInformation PartnerRole="NotifyParty1">
                    <PartnerName>{{ data.name }}</PartnerName>
                    <ContactInformation>
                        <ContactName ContactType="Informational">{{ data.name }}</ContactName>
                    </ContactInformation>
                    {% if data.address %}
                    {% set address = data.address %}
                    <AddressInformation>
                        {% if address[0:35] %}
                        <AddressLine>{{ address[0:35] }}</AddressLine>
                        {% endif %}
                        {% if address[35:70] %}
                        <AddressLine>{{ address[35:70] }}</AddressLine>
                        {% endif %}
                        {% if address[70:105] %}
                        <AddressLine>{{ address[70:105] }}</AddressLine>
                        {% endif %}
                        {% if address[105:140] %}
                        <AddressLine>{{ address[105:140] }}</AddressLine>
                        {% endif %}
                    </AddressInformation>
                    {% endif %}
                </PartnerInformation>
                {% endif %}
                {% endfor %}
            {% endif %}


                {% if additional_notify_party_2 %}
                {% for data in additional_notify_party_2 %}
                {% if data.name %}
                <PartnerInformation PartnerRole="NotifyParty2">
                    <PartnerName>{{ data.name }}</PartnerName>
                    <ContactInformation>
                        {% if data.name %}
                        <ContactName ContactType="Informational">{{ data.name }}</ContactName>
                        {% endif %}
                        {% if data.fax %}
                        <CommunicationValue CommunicationType="Fax">{{ data.fax }}</CommunicationValue>
                        {% endif %}
                        {% if data.email %}
                        <CommunicationValue CommunicationType="Email">{{ data.email }}</CommunicationValue>
                        {% endif %}
                        {% if data.phone %}
                        <CommunicationValue CommunicationType="Telephone">{{ data.phone }}</CommunicationValue>
                        {% endif %}
                    </ContactInformation>
                    {% if data.address_lines %}
                    {% set address_lines = data.address_lines %}
                    <AddressInformation>
                        {% if address_lines[0:35] %}
                        <AddressLine>{{ address_lines[0:35] }}</AddressLine>
                        {% endif %}
                        {% if address_lines[35:70] %}
                        <AddressLine>{{ address_lines[35:70] }}</AddressLine>
                        {% endif %}
                        {% if address_lines[70:105] %}
                        <AddressLine>{{ address_lines[70:105] }}</AddressLine>
                        {% endif %}
                        {% if address_lines[105:140] %}
                        <AddressLine>{{ address_lines[105:140] }}</AddressLine>
                        {% endif %}
                    </AddressInformation>
                    {% endif %}
                </PartnerInformation>
                {% endif %}
                {% endfor %}
            {% elif manual_additional_notify_party_2 %}
                {% for data in manual_additional_notify_party_2 %}
                {% if data.name %}
                <PartnerInformation PartnerRole="NotifyParty2">
                    <PartnerName>{{ data.name }}</PartnerName>
                    <ContactInformation>
                        <ContactName ContactType="Informational">{{ data.name }}</ContactName>
                    </ContactInformation>
                    {% if data.address %}
                    {% set address = data.address %}
                    <AddressInformation>
                        {% if address[0:35] %}
                        <AddressLine>{{ address[0:35] }}</AddressLine>
                        {% endif %}
                        {% if address[35:70] %}
                        <AddressLine>{{ address[35:70] }}</AddressLine>
                        {% endif %}
                        {% if address[70:105] %}
                        <AddressLine>{{ address[70:105] }}</AddressLine>
                        {% endif %}
                        {% if address[105:140] %}
                        <AddressLine>{{ address[105:140] }}</AddressLine>
                        {% endif %}
                    </AddressInformation>
                    {% endif %}
                </PartnerInformation>
                {% endif %}
                {% endfor %}
            {% endif %}


                {% if partner_notification %}
                <PartnerInformation PartnerRole="MessageRecipient">
                    <ContactInformation>
                        <ContactName ContactType="SINotification">{{ partner_notification.contact_name }}</ContactName>
                        {% for email in partner_notification.emails %}
                        <CommunicationValue CommunicationType="Email">{{ email }}</CommunicationValue>
                        {% endfor %}
                    </ContactInformation>
                </PartnerInformation>
                {% endif %}
                {% if consolidatorstuffer_name %}
                <PartnerInformation PartnerRole="Consolidator">
                    <PartnerName>{{ consolidatorstuffer_name }}</PartnerName>
                    {% if consolidatorstuffer_address %}
                    <AddressInformation>
                        <AddressLine>{{ consolidatorstuffer_address }}</AddressLine>
                    </AddressInformation>
                    {% endif %}                    
                </PartnerInformation>
                {% endif %}
                {% if contract_party and contract_party[0].party_name %}
                <PartnerInformation PartnerRole="ContractParty">
                    <PartnerName>{{ contract_party[0].party_name }}</PartnerName>

                    {% if contract_party[0].email or contract_party[0].phone %}
                    <ContactInformation>
                        {% if contract_party[0].party_name %}
                        <ContactName ContactType="Informational">{{ contract_party[0].party_name }}</ContactName>
                        {% endif %}
                        {% if contract_party[0].phone %}
                        <CommunicationValue CommunicationType="Telephone">{{ contract_party[0].phone }}</CommunicationValue>
                        {% endif %}
                        {% if contract_party[0].email %}
                        <CommunicationValue CommunicationType="Email">{{ contract_party[0].email }}</CommunicationValue>
                        {% endif %}
                        {% if contract_party[0].fax %}
                        <CommunicationValue CommunicationType="Fax">{{ contract_party[0].fax }}</CommunicationValue>
                        {% endif %}
                    </ContactInformation>
                    {% endif %}

                    {% if contract_party[0].address_lines %}
                    {% set address_lines = contract_party[0].address_lines %}
                    <AddressInformation>
                        {% if address_lines[0:35] %}
                        <AddressLine>{{ address_lines[0:35] }}</AddressLine>
                        {% endif %}
                        {% if address_lines[35:70] %}
                        <AddressLine>{{ address_lines[35:70] }}</AddressLine>
                        {% endif %}
                        {% if address_lines[70:105] %}
                        <AddressLine>{{ address_lines[70:105] }}</AddressLine>
                        {% endif %}
                        {% if address_lines[105:140] %}
                        <AddressLine>{{ address_lines[105:140] }}</AddressLine>
                        {% endif %}
                    </AddressInformation>
                    {% endif %}
                </PartnerInformation>
                {% endif %}


                {% if freight_payer_name %}
                <PartnerInformation PartnerRole="FreightPayer">
                    <PartnerName>{{ freight_payer_name }}</PartnerName>
                    {% if freight_payer_address %}
                    <AddressInformation>
                        <AddressLine>{{ freight_payer_address }}</AddressLine>
                    </AddressInformation>
                    {% endif %}
                </PartnerInformation>
                {% endif %}
                {% if importer %}
                <PartnerInformation PartnerRole="Importer">
                    <PartnerName>{{ importer }}</PartnerName>                    
                    {% if importer_address %}
                    <AddressInformation>
                        <AddressLine>{{ importer_address }}</AddressLine>
                    </AddressInformation>
                    {% endif %}
                </PartnerInformation>
                {% endif %}
                {% if manufacturersupplier_name %}
                <PartnerInformation PartnerRole="SupplierManufacturer">
                    <PartnerName>{{ manufacturersupplier_name }}</PartnerName>
                    {% if manufacturersupplier_address %}
                    <AddressInformation>
                        <AddressLine>{{ manufacturersupplier_address }}</AddressLine>
                    </AddressInformation>
                    {% endif %}
                </PartnerInformation>
                {% endif %}
                {% for data in carrier %}
                {% if carrier %}
                <PartnerInformation PartnerRole="Carrier">
                    {% if data.party_code %}
                    <PartnerIdentifier Agency="AssignedBySender">{{ data.party_code }}</PartnerIdentifier>
                    {% endif %}
                    {% if data.part_name %}
                    <PartnerName>{{ data.part_name }}</PartnerName>
                    {% endif %}                    
                    {% if data.address_lines %}
                    {% set address_lines = data.address_lines %}
                    <AddressInformation>
                        {% if address_lines[0:35] %}
                        <AddressLine>{{ address_lines[0:35] }}</AddressLine>
                        {% endif %}
                        {% if address_lines[35:70] %}
                        <AddressLine>{{ address_lines[35:70] }}</AddressLine>
                        {% endif %}
                        {% if address_lines[70:105] %}
                        <AddressLine>{{ address_lines[70:105] }}</AddressLine>
                        {% endif %}
                        {% if address_lines[105:140] %}
                        <AddressLine>{{ address_lines[105:140] }}</AddressLine>
                        {% endif %}
                        {% if data.postal_code %}
                        <PostalCode>{{ data.postal_code }}</PostalCode>
                        {% endif %}
                        {% if data.country_code %}
                        <CountryCode>{{ data.country_code }}</CountryCode>
                        {% endif %}                        
                    </AddressInformation>
                    {% endif %}
                </PartnerInformation>
                {% endif %}
                {% endfor %}
                {% if door_delivery_name %}
                <PartnerInformation PartnerRole="ShipTo">
                    <PartnerName>{{ door_delivery_name }}</PartnerName>
                    <ContactInformation>
                        {% if door_delivery_name %}
                        <ContactName ContactType="Informational">{{ door_delivery_name }}</ContactName>
                        {% endif %}
                        {% if door_delivery_fax %}
                        <CommunicationValue CommunicationType="Fax">{{ door_delivery_fax }}</CommunicationValue> 
                        {% endif %}   
                        {% if door_delivery_email %}                    
                        <CommunicationValue CommunicationType="Email">{{ door_delivery_email }}</CommunicationValue>
                        {% endif %}
                        {% if door_delivery_phone %}
                        <CommunicationValue CommunicationType="Telephone">{{ door_delivery_phone }}</CommunicationValue>
                        {% endif %}                        
                    </ContactInformation>
                    {% if door_delivery_address_lines %}
                    {% set address_lines = door_delivery_address_lines %}
                    <AddressInformation>
                    {% if address_lines[0:35] %}
                    <AddressLine>{{ address_lines[0:35] }}</AddressLine>
                    {% endif %}
                    {% if address_lines[35:70] %}
                    <AddressLine>{{ address_lines[35:70] }}</AddressLine>
                    {% endif %}
                    {% if address_lines[70:105] %}
                    <AddressLine>{{ address_lines[70:105] }}</AddressLine>
                    {% endif %}
                    {% if address_lines[105:140] %}
                    <AddressLine>{{ address_lines[105:140] }}</AddressLine>
                    {% endif %}
                </AddressInformation>
                {% endif %}
                </PartnerInformation>
                {% endif %}
            </Parties>
        </MessageProperties>
        <MessageDetails>
                {% for container in equipment %}
                <EquipmentDetails>
                    <LineNumber>{{ loop.index }}</LineNumber>
                    {% if container.equipment_name %}
                    <EquipmentIdentifier EquipmentSupplier="Carrier">{{ container.equipment_name }}</EquipmentIdentifier>
                    {% endif %}
                    <EquipmentType>
                        <EquipmentTypeCode>{{ container.code_value }}</EquipmentTypeCode>
                        {% if container.description %}
                        <EquipmentDescription>{{ container.description }}</EquipmentDescription>
                        {% endif %}
                    </EquipmentType>
                    {% if container.weight_value %}
                    <EquipmentGrossWeight UOM="KGM">{{ container.weight_value }}</EquipmentGrossWeight>
                    {% endif %}
                    {% if container.volume %}
                    <EquipmentGrossVolume UOM="MTQ">{{ container.volume }}</EquipmentGrossVolume>
                    {% endif %}
                    {% if container.carrier_seal_number %}
                    <EquipmentSeal SealingParty="Carrier">{{ container.carrier_seal_number }}</EquipmentSeal>
                    {% endif %}
                    {% if container.shipper_seal_number %}
                    <EquipmentSeal SealingParty="Shipper">{{ container.shipper_seal_number }}</EquipmentSeal>
                    {% endif %}
                    {% if container.comments %}
                    <EquipmentComments CommentType="Equipment">{{ container.comments }}</EquipmentComments>
                    {% endif %}                    
                    {% if container.cargo.get('cus_code') %}                    
                    <EquipmentReferenceInformation ReferenceType="CustomsReleaseCode">{{container.cargo.get('cus_code')}}</EquipmentReferenceInformation>                     
                    {% endif %}                     
                </EquipmentDetails>
                {% endfor %}                
                <GoodsDetails>
                    {% if equipment %}
                    {% set good = equipment[0] %}                    
                    <LineNumber>1</LineNumber>                    
                    <PackageDetail Level="outer">
                        <NumberOfPackages>{{ total_number_of_packages }}</NumberOfPackages>
                        <PackageTypeCode>{{ good.cargo.xmlcode }}</PackageTypeCode>
                        <PackageTypeDescription>{{ good.cargo.standard_name }}</PackageTypeDescription>
                    </PackageDetail>                    
                    {% for line in good.description_lines %}
                    <PackageDetailComments CommentType="GoodsDescription">{{ line }}</PackageDetailComments>
                    {% endfor %}                    
                    {% if good.cargo.hs_code %}
                    <ProductId ItemTypeIdCode="HarmonizedSystem">{{ good.cargo.hs_code }}</ProductId>
                    {% endif %}
                    {% if good.cargo.get('ncm_codes') %}
                    <ProductId ItemTypeIdCode="NCMCode">{{ good.cargo.get('ncm_codes') }}</ProductId>
                    {% endif %}
                    {% if good.cargo.get('schedule_b_number') %}
                    <ProductId ItemTypeIdCode="USCensusScheduleB">{{ good.cargo.get('schedule_b_number') }}</ProductId>
                    {% endif %}                
                    {% if total_shipment_volume %}
                    <PackageDetailGrossVolume UOM="MTQ">{{ total_shipment_volume }}</PackageDetailGrossVolume>
                    {% endif %}                    
                    {% if total_shipment_weight %}
                    <PackageDetailGrossWeight UOM="KGM">{{ total_shipment_weight }}</PackageDetailGrossWeight>
                    {% endif %}  
                    {% if good.cargo.get('marks_and_numbers') %}
                    <PackageMarks>
                        <Marks>{{ good.cargo.get('marks_and_numbers') }}</Marks>                        
                    </PackageMarks>  
                    {% endif %}                                    
                    {% for container in equipment %}
                    {% if container.equipment_name %}
                    <SplitGoodsDetails>
                        <EquipmentIdentifier>{{ container.equipment_name }}</EquipmentIdentifier>
                        <SplitGoodsNumberOfPackages>{{ container.cargo.package_count }}</SplitGoodsNumberOfPackages>
                        <SplitGoodsGrossVolume UOM="MTQ">{{ container.cargo.gross_volume }}</SplitGoodsGrossVolume>
                        <SplitGoodsGrossWeight UOM="KGM">{{ container.cargo.cargo_gross_weight }}</SplitGoodsGrossWeight>
                    </SplitGoodsDetails>
                    {% endif %}
                    {% endfor %}                    
                {% endif %}
                </GoodsDetails> 
                </MessageDetails>
                </MessageBody>
    </Message>