import datetime
import frappe
from frappe import _
from frappe.model.document import Document
from frappe.utils.response import build_response
from frappe.utils.response import json_handler
from frappe.utils import cint, now
import json
from westside.www.Authentication.auth_decorators import role_required
from westside.www.Web_API.shipping_instruct import generate_shipping_instruction_xml
from westside.www.Web_API.currency_type import get_equipment_details


@frappe.whitelist(allow_guest=True, methods=["POST"])
def create_shipping_request():
    try:
        request_data = frappe.request.data
        if not request_data:
            return build_response({"status_code": 400, "message": "No data provided"})

        try:
            data = json.loads(request_data)
        except json.JSONDecodeError as e:
            return build_response({"status_code": 400, "message": f"Invalid JSON: {str(e)}"})

        frappe.db.begin()
       

        # booking_request_id = data.get("booking_request_id")
        # if booking_request_id:
        #     existing_si = frappe.get_all("Shipping Instructions", 
        #         filters={"booking_request_id": booking_request_id},
        #         fields=["name"],
        #         order_by="creation desc"
        #     )

        #     if existing_si:
        #         latest_si = existing_si[0]["name"]

        #         inttra_status_doc = frappe.get_all("Inttra Status SI",
        #             filters={"shipping_instruction": latest_si},
        #             fields=["si_inttra_status", "si_carrier_status"]
        #         )

        #         if inttra_status_doc:
        #             status = inttra_status_doc[0]
        #             inttra = (status.get("si_inttra_status") or "").lower()
        #             carrier = (status.get("si_carrier_status") or "").lower()

        #             if inttra == "rejected":
        #                 pass 
        #             elif inttra == "accepted" and carrier == "rejected":
        #                 pass 
        #             elif inttra == "accepted" and carrier == "pending":
        #                 return {
        #                     "status_code": 409,
        #                     "message": f"Cannot create new Shipping Instruction for booking {booking_request_id} because INTTRA status is 'Accepted' and Carrier status is 'Pending'.",
        #                     "existing_si": latest_si
        #                 }
        #             else:
        #                 return {
        #                     "status_code": 409,
        #                     "message": f"Shipping Instruction already exists for booking {booking_request_id}.Cannot create new SI unless Inttra or Carrier status is rejected.",
        #                     "existing_si": latest_si
        #                 }
        #         else:
        #             return {
        #                 "status_code": 409,
        #                 "message": f"Shipping Instruction already exists for booking {booking_request_id}",
        #                 "existing_si": latest_si
        #             }

        booking_request_id = data.get("booking_request_id")
        bl_no = None

        if booking_request_id:
            bln_entry = frappe.get_all(
                "Reference Information",
                filters={
                    "parent": booking_request_id,
                    "reference_type": "BillOfLadingNumber"
                },
                fields=["text"],                
            )
            if bln_entry:
                bl_no = bln_entry[0].get("text")

        consignee = data.get("consignee")
        if consignee and not frappe.db.exists("Customer DB", consignee):
            frappe.throw(f"Customer DB '{consignee}' does not exist. Please enter a valid Consignee.")

        shipper = data.get("shipper")
        if shipper and not frappe.db.exists("Shipper", shipper):
            frappe.throw(f"Shipper '{shipper}' does not exist. Please enter a valid Shipper.")

        notify_party = data.get("notify_party")
        if notify_party and not frappe.db.exists("Customer DB", notify_party):
            frappe.throw(f"Notify Party '{notify_party}' does not exist. Please enter a valid Notify Party.")        
        
        origin_place_of_carrier_receipt = data.get("origin_place_of_carrier_receipt")
        if origin_place_of_carrier_receipt and not frappe.db.exists("UNLOCODE Locations", origin_place_of_carrier_receipt):
            frappe.throw(f"UNLOCODE Locations '{origin_place_of_carrier_receipt}' does not exist. Please enter a valid Place of Carrier Receipt.")

        port_of_load = data.get("port_of_load")
        if port_of_load and not frappe.db.exists("UNLOCODE Locations", port_of_load):
            frappe.throw(f"UNLOCODE Locations '{port_of_load}' does not exist. Please enter a valid Port of Load.") 
        
        destination_place_of_carrier_delivery = data.get("destination_place_of_carrier_delivery")
        if destination_place_of_carrier_delivery and not frappe.db.exists("UNLOCODE Locations", destination_place_of_carrier_delivery):
            frappe.throw(f"UNLOCODE Locations '{destination_place_of_carrier_delivery}' does not exist. Please enter a valid Place of Carrier Delivery.")
            
       
        shipping_request = frappe.get_doc({
            "doctype": "Shipping Instructions",
            # Detail
            "bookingnumber": data.get("bookingnumber"),
            "creationdate": data.get("creationdate"),
            "booking_request_id": data.get("booking_request_id"),
            
            #shipper details
            "shipper": data.get("shipper"),
            "custom_shipper_mobile_no": data.get("custom_shipper_mobile_no"),
            "custom_shipper_name": data.get("custom_shipper_name"),
            "shipper_address": data.get("shipper_address"),
            "shipper_email": data.get("shipper_email"),
        
            # Forwarder Details
            "forwarder": data.get("forwarder"),
            "forwarder_address": data.get("forwarder_address"),

            "carrier": data.get("carrier"),
            "carrier_booking_number": data.get("carrier_booking_number"),
            "carrier_email": data.get("carrier_email"),

            # Consignee Details
            
            "consignee": data.get("consignee"),
            "consignee_address": data.get("consignee__address"),
            
            # Notify Party Details
            "notify_party": data.get("notify_party"),
            "additional_notify_party_1": data.get("additional_party_one"),
            "additional_notify_party_2": data.get("additional_party_two"),

            # Contract Party Details 
            "contract_party": data.get("contract_party") or None,
            "contract_party_address": data.get("contract_party_address") if data.get("contract_party") else None,
            "contract_party_name_entry": data.get("contract_party_name_entry") if not data.get("contract_party") else None,
            "contract_party_address_entry": data.get("contract_party_address_entry") if not data.get("contract_party") else None,


            # Freight Payer Details
            "freight_payer": data.get("freight_payer"),
            "freight_payer_address": data.get("freight_payer_address"),

            # Manufacturer/Supplier Details
            "manufacturersupplier": data.get("manufacturersupplier"),
            "manufacturersupplier_address": data.get("manufacturersupplier_address"),

            # Consolidator/Stuffer Details
            "consolidatorstuffer": data.get("consolidatorstuffer"),
            "consolidatorstuffer_address": data.get("consolidatorstuffer_address"),

            # Warehouse Keeper Details
            "warehouse_keeper": data.get("warehouse_keeper"),
            "warehouse_keeper_address": data.get("warehouse_keeper_address"),

            # Importer Details
            "importer": data.get("importer"),
            "importer_address": data.get("importer_address"),

            # Transport Details
            "vessel": data.get("vessel"),
            "bl_no": bl_no or "",
            "imo_number": data.get("imo_number"),
            "origin_place_of_carrier_receipt": data.get("origin_place_of_carrier_receipt"),
            "print_on_bl_asorigin": data.get("print_on_bl_asorigin"),
            "shipment_type": data.get("shipment_type"),
            "origin_name": data.get("origin_name"),
            "print_on_bl_aspol": data.get("print_on_bl_aspol"),
            "voyage": data.get("voyage"),
            "port_of_load": data.get("port_of_load"),
            "print_on_bl_aspod": data.get("print_on_bl_aspod"),
            "move_type": data.get("move_type"),
            "port_of_load_name": data.get("port_of_load_name"),
            "print_on_bl_asdestination": data.get("print_on_bl_asdestination"),
            "door_delivery_address": data.get("door_delivery_address"),
            "port_of_discharge": data.get("port_of_discharge"),
            "port_of_discharge_name": data.get("port_of_discharge_name"),
            "destination_place_of_carrier_delivery": data.get("destination_place_of_carrier_delivery"),
            "destination_name": data.get("destination_name"),

            # Government Tax IDs
            "shipper_tax_id": data.get("shipper_tax_id"),
            "shipper_iec": data.get("shipper_iec"),
            "shipper_pan_no": data.get("shipper_pan_no"),
            "notify_party_tax_id": data.get("notify_party_tax_id"),
            "consignee_tax_id": data.get("consignee_tax_id"),


            # ICS2 Entry Summary Declaration
            "are_the_goods_going_to_be_delivered_to_these_countries": data.get("are_the_goods_going_to_be_delivered_to_these_countries"),
            "do_you_prefer_the_carrier_to_declare_the_entry_summary_ens": data.get("do_you_prefer_the_carrier_to_declare_the_entry_summary_ens"),
            "have_house_bills_been_issued_for_this_shipment": data.get("have_house_bills_been_issued_for_this_shipment"),


            # Cargo Identification Numbers
            "pcin": data.get("pcin"),
            "csn": data.get("csn"),
            "acid_number_mcin": data.get("acid_number_mcin"),

            
            # Particulars
            "i_am_shipping_only_one_cargo_in_this_shipment": data.get("i_am_shipping_only_one_cargo_in_this_shipment"),
            "total_number_of_containers": data.get("total_number_of_containers"),
            "total_number_of_packages": data.get("total_number_of_packages"),

            # Control Totals
            "total_shipment_weight": data.get("total_shipment_weight"),
            "total_shipment_volume": data.get("total_shipment_volume"),

            # Shipper's Declared Value
            "currency_type": data.get("currency_type"),
            "shippers__declared_value": data.get("shippers_declared_value"),
        
            # B/L Preferences
            "bl_release_office__location": data.get("bl_release_office_location"),
            "print_on_bl_aslocation": data.get("bl_release_office_location"),
            "requested_date_of_issue": data.get("requested_date_of_issue"),
            "bill_type": data.get("bill_type"),

            
            # B/L Print Instructions
            "freightedno_of_documents": data.get("freightedno_of_documents"),
            "unfreightedno_of_documents": data.get("unfreightedno_of_documents"),
            "non_negotiable_freightedno_of_copies": data.get("non_negotiable_freightedno_of_copies"),
            "non_negotiable_unfreighted_no_of_copies": data.get("non_negotiable_unfreighted_no_of_copies"),
            "bl_comments": data.get("bl_comments"),


            # Notification Emails
            "si_requestor_emails": data.get("si_requestor_emails"),
            "partner_notification_emails": data.get("partner_notification_emails"),
            "si_name": data.get("si_name"),
            "shipper_reference_number": data.get("shipper_reference_number"),
            "forwarder_reference_number": data.get("forwarder_reference_number"),
            "transaction_number": data.get("transaction_number"),
            "unique_consignment_reference": data.get("unique_consignment_reference"),
            "purchase_order_number": data.get("purchase_order_number"),
            "contract_reference_number": data.get("contract_reference_number"),
            "bl_reference_number": data.get("bl_reference_number"),
            "exporter_reference_number": data.get("exporter_reference_number"),
            "consignee_order_number": data.get("consignee_order_number"),
            "invoice_reference_number": data.get("invoice_reference_number"),
            "letter_of_credit_reference": data.get("letter_of_credit_reference"),
            "lcr_issue_date": data.get("lcr_issue_date"),
            "lcr_expiry_date": data.get("lcr_expiry_date"),
            "customs_house_broker_reference": data.get("customs_house_broker_reference"),
            "government_reference_or_fmc_number": data.get("government_reference_or_fmc_number"),
            "export_license_number": data.get("export_license_number"),
            # "export_license_issue_date": data.get("export_license_issue_date"),
            # "export_license_expiry_date": data.get("export_license_expiry_date"),
            "eln_issue_date": data.get("export_license_issue_date"),
            "eln_expiry_date": data.get("export_license_expiry_date"),
            "house_bill_number": data.get("house_bill_number"),
            "si_customer_name_entry": data.get("si_customer_name_entry"),
            "si_customer_address_entry": data.get("si_customer_address_entry"),
            "si_notify_party_name_entry": data.get("si_notify_party_name_entry"),
            "si_notify_party_address_entry": data.get("si_notify_party_address_entry"),
            "additional_party_one_name_entry": data.get("additional_party_one_name_entry"),
            "additional_party_one_address": data.get("additional_party_one_address"),
            "additional_party_two_name_entry": data.get("additional_party_two_name_entry"),
            "additional_party_two_address": data.get("additional_party_two_address"),
            "doc_version": "1.0" or "",
            "message_status": "Original" or "",
        })

        # Freight Charges - child table
        if isinstance(data.get("freight_charges"), list):
            for charge in data["freight_charges"]:
                shipping_request.append("freight_charges", {
                    "charge_type": charge.get("charge_type"),
                    "payer": charge.get("payer"),
                    "freight_term": charge.get("freight_term"),
                    "payment_location": charge.get("payment_location"),
                    "type": charge.get("type"),
                })

        
        shipping_request.insert()
        try:
            record_si_version_history(shipping_request, remarks="Initial creation")
        except Exception as e:
            frappe.log_error(frappe.get_traceback(), "Record SI Version History Error")
            frappe.msgprint("SI created, but failed to log version history.")

        if not isinstance(data.get("container"), list):
            frappe.throw("Container must be a list of dictionaries")

        booking_id = data.get("booking_request_id")
        if not booking_id:
            frappe.throw("Missing booking_request_id")

        # Fetch all existing equipment for the booking
        existing_equipments = frappe.get_all(
            "Equipments",
            filters={"booking_request": booking_id},
            fields=["name", "equipment_name"]
        )

        dct_eqp_name_map = {}
        lst_non_eqp_names = []
        for e in existing_equipments:
            if e["equipment_name"]:
                dct_eqp_name_map[e["equipment_name"]] = e["name"]
            else:
                lst_non_eqp_names.append(e["name"])

        # Fallback values from first container’s first cargo
        first_cargo = data.get("container", [])[0].get("cargo", [])[0] if data.get("container") and data["container"][0].get("cargo") else {}
        global_hs_code = first_cargo.get("hs_code", "").strip()
        global_description = first_cargo.get("cargo_description", "").strip()

        for item in data.get("container", []):
            if not isinstance(item, dict):
                continue

            container_number = item.get("container_number")
            if not container_number:
                continue

            equipment_values = {
                "si_id": shipping_request.name,
                "booking_request": booking_id,
                "equipment_name": container_number,
                "container_type_id": item.get("container_type"),
                "comment": item.get("comment"),
                "supplier_type": item.get("container_supplier"),
                "service_type": item.get("service_type"),
                "weight_value": item.get("weight_value"),
                "weight_type": item.get("weight_type"),
                "wood_declaration": item.get("wood_declaration"),
                "carrier_seal_number": item.get("carrier_seal_numbers"),
                "shipper_seal_number": item.get("shipper_seal_numbers"),
                "tare_weight": item.get("container_tare_weight"),
                "cargo_weight": item.get("cargo_weight"),
                "gross_weight": item.get("gross_weight"),
                "doc_version": "1.0",
                "message_status": "Original",

            }

            equipment_id = dct_eqp_name_map.get(container_number)

            if equipment_id:
                equipment_doc = frappe.get_doc("Equipments", equipment_id)
                equipment_doc.update(equipment_values)
                equipment_doc.save(ignore_permissions=True)

            elif lst_non_eqp_names:
                equipment_id = lst_non_eqp_names.pop(0)
                equipment_doc = frappe.get_doc("Equipments", equipment_id)
                equipment_doc.update(equipment_values)
                equipment_doc.save(ignore_permissions=True)

            else:
                equipment_doc = frappe.get_doc({"doctype": "Equipments", **equipment_values})
                equipment_doc.insert(ignore_permissions=True)

            equipment_doc.set("cargo", [])

            for cargo in item.get("cargo", []):
                if not isinstance(cargo, dict):
                    continue

                hs_code = global_hs_code or cargo.get("hs_code", "").strip() 
                description = global_description or cargo.get("cargo_description", "").strip() 

                cargo_entry = cargo.copy()
                cargo_entry["hs_code"] = hs_code
                cargo_entry["cargo_description"] = description

                equipment_doc.append("cargo", cargo_entry)

            equipment_doc.save(ignore_permissions=True)
        frappe.db.commit()

        xml_content = generate_shipping_instruction_xml(shipping_request.name)
        if xml_content["status"] != "success":
            frappe.db.rollback()
            return {
                "status_code": 500,
                "message": "Failed to generate XML. Please check the data."
            }
        return {
            "xml_content": xml_content,
            "status_code": 200, 
            "message": "Shipping Instruction created successfully",
            "data": shipping_request.name
        }

    except Exception as e:
        frappe.db.rollback()
        frappe.log_error(frappe.get_traceback(), "Shipping Instruction API Error")
        return {"status_code": 500, "message": f"Error: {str(e)}"}









@frappe.whitelist(allow_guest=True)
def get_shipping_instruction_data(booking_request_id):
    if not booking_request_id:
        frappe.throw(_("Booking Request ID is required"))

    booking_doc = frappe.get_doc("Booking Request", booking_request_id)

    # Convert to dict and remove metadata
    doc_data = booking_doc.as_dict()
    remove_metadata_fields(doc_data)
    meta = frappe.get_meta(booking_doc.doctype)

    total_containers = 0
    if hasattr(booking_doc, "containers") and booking_doc.containers:
        for container in booking_doc.containers:
            total_containers += cint(container.number_of_containers or 0)
    doc_data["total_number_of_containers"] = total_containers



    for fieldname in list(doc_data.keys()):
        value = doc_data[fieldname]
        if not value:
            continue

        field = meta.get_field(fieldname)
        if not field:
            continue

        # Resolve Link fields
        if field.fieldtype == "Link":
            linked_doctype = field.options
            try:
                if frappe.db.exists(linked_doctype, value) and linked_doctype != "Booking Request":
                    linked_doc = frappe.get_doc(linked_doctype, value)
                    doc_data[fieldname] = linked_doc.as_dict()
            except frappe.PermissionError:
                doc_data[fieldname] = None  

        # Resolve Table (child table) fields
        elif field.fieldtype == "Table":
            child_data = []
            for child in booking_doc.get(fieldname):
                child_dict = child.as_dict()
                

                # Optional: also resolve link fields inside child table rows
                child_meta = frappe.get_meta(child.doctype)
                for child_fieldname in list(child_dict.keys()):
                    child_value = child_dict[child_fieldname]
                    if not child_value:
                        continue

                    child_field = child_meta.get_field(child_fieldname)
                    if child_field and child_field.fieldtype == "Link":
                        linked_doctype = child_field.options
                        try:
                            if frappe.db.exists(linked_doctype, child_value):
                                linked_doc = frappe.get_doc(linked_doctype, child_value)
                                child_dict[child_fieldname] = linked_doc.as_dict()
                        except frappe.PermissionError:
                            child_dict[child_fieldname] = None
                child_data.append(child_dict)

            doc_data[fieldname] = child_data

    # Map movement type
    movement_value = booking_doc.move_type or ""
    simplified_move_type = ""
    if movement_value == "Port, Ramp, or CY to Port, Ramp, or CY":
        simplified_move_type = "PortToPort"
    elif movement_value == "Door to Port, Ramp, or CY":
        simplified_move_type = "DoorToPort"
    elif movement_value == "Door to Door":
        simplified_move_type = "DoorToDoor"
    elif movement_value == "Port, Ramp, or CY to Door":
        simplified_move_type = "PortToDoor"

    move_type_map = {
        "PortToPort": "Port,Ramp,CY/CFS to Port,Ramp,CY/CFS",
        "DoorToPort": "Door to Port,Ramp,CY/CFS",
        "DoorToDoor": "Door To Door",
        "PortToDoor": "Port,Ramp,CY/CFS to Door"
    }

    readable_value = move_type_map.get(simplified_move_type)
    doc_data["move_type"] = readable_value
    
    port_of_load_code = ""
    port_of_discharge_code = ""
    vessel = ""
    port_of_load_meta = {}
    port_of_discharge_meta = {}

    if booking_doc.main_carriage:
        lst_main_carriage = sorted(booking_doc.main_carriage, key=lambda x: x.etd or datetime.datetime.min)
        main_carriage_load = lst_main_carriage[0]
        main_carriage_disc = lst_main_carriage[-1]
        port_of_load_code = main_carriage_load.port_of_load or ""
        port_of_discharge_code = main_carriage_disc.port_of_discharge or ""
        vessel = main_carriage_load.vessel or ""

        # Fetch full metadata for port_of_load
        if port_of_load_code:
            try:
                port_of_load_meta = frappe.get_doc("UNLOCODE Locations", port_of_load_code).as_dict()
                remove_metadata_fields(port_of_load_meta)  # optional
            except frappe.DoesNotExistError:
                port_of_load_meta = {}

        # Fetch full metadata for port_of_discharge
        if port_of_discharge_code:
            try:
                port_of_discharge_meta = frappe.get_doc("UNLOCODE Locations", port_of_discharge_code).as_dict()
                remove_metadata_fields(port_of_discharge_meta)  # optional
            except frappe.DoesNotExistError:
                port_of_discharge_meta = {}

    doc_data["port_of_load"] = port_of_load_meta
    doc_data["port_of_discharge"] = port_of_discharge_meta
    doc_data["vessel"] = vessel


    
    doc_data["origin_location_name"] = frappe.db.get_value(
        "UNLOCODE Locations", booking_doc.place_of_carrier_receipt, "location_name"
    ) or ""

    doc_data["destination_location_name"] = frappe.db.get_value(
        "UNLOCODE Locations", booking_doc.place_of_carrier_delivery, "location_name"
    ) or ""

    doc_data["creationdate"] = frappe.utils.now_datetime()


    
    carrier_info = {}

    if booking_doc.booking_agent:
        try:
            booking_agent = frappe.get_doc("Carrier", booking_doc.booking_agent)
            carrier_info = {
                "name": booking_agent.name,
                "partyalias": booking_agent.partyalias,
                "partyname1": booking_agent.partyname1,
                "inttra_id": booking_agent.inttra_id,
                "address": booking_agent.address,
                "postal_code": booking_agent.postal_code,
                "country_code": booking_agent.country_code,
            }
        except frappe.DoesNotExistError:
            carrier_info = {}  # No booking agent found, leave it empty

    doc_data["carrier"] = carrier_info
    


    equipment_docs = frappe.get_all("Equipments", filters={"booking_request": booking_request_id}, pluck="name" )
    equipments_data = []

    for eq_id in equipment_docs:
        eq_doc = frappe.get_doc("Equipments", eq_id)
        eq_data = eq_doc.as_dict()
        remove_metadata_fields(eq_data)
        # Process each field in the equipment document
        for fieldname, value in eq_data.items():
            if not value:
                continue
            
            # Only process Link and Table fieldtypes
            if isinstance(value, str):
                # Handle Link field
                field = frappe.get_meta(eq_doc.doctype).get_field(fieldname)
                if field and field.fieldtype == "Link":
                    linked_doctype = field.options
                    try:
                        if frappe.db.exists(linked_doctype, value) and linked_doctype != "Booking Request"  :
                            linked_doc = frappe.get_doc(linked_doctype, value)
                            eq_data[fieldname] = linked_doc.as_dict()
                    except frappe.PermissionError:
                        eq_data[fieldname] = None
            
            elif isinstance(value, list):
                # Handle Table field
                child_data = []
                for child in eq_doc.get(fieldname):
                    child_dict = child.as_dict()
                    remove_metadata_fields(child_dict)
                    for child_fieldname, child_value in child_dict.items():
                        if child_value:
                            child_field = frappe.get_meta(child.doctype).get_field(child_fieldname)
                            if child_field and child_field.fieldtype == "Link":
                                linked_doctype = child_field.options
                                
                                try:
                                    if frappe.db.exists(linked_doctype, child_value):
                                        linked_doc = frappe.get_doc(linked_doctype, child_value)
                                        child_dict[child_fieldname] = linked_doc.as_dict()
                                except frappe.PermissionError:
                                    child_dict[child_fieldname] = None
                    child_data.append(child_dict)
                eq_data[fieldname] = child_data

        equipments_data.append(eq_data)

    # 👇 Include enriched Equipments data
    doc_data["equipments"] = equipments_data

    return doc_data





def remove_metadata_fields(doc_data):
    """Remove Frappe-specific metadata fields."""
    metadata_fields = [
        'owner', 'creation', 'modified', 'modified_by',
        'parent', 'parentfield', 'parenttype', 'idx',
        'docstatus', 'amended_from', '_user_tags',
        '_comments', '_assign', '_liked_by','booking_agent','booking_office','contract_number',
    ]
    for field in metadata_fields:
        doc_data.pop(field, None)






@frappe.whitelist(allow_guest=True)
def create_si_template(template_name, si_data, si_id=None):
    if not template_name or not si_data:
        return {"status": "error", "message": _("Missing required fields.")}

    try:
        # if si_data is a dict, convert to string
        if isinstance(si_data, dict):
            si_data = json.dumps(si_data)

        doc = frappe.get_doc({
            "doctype": "Shipping Instruction Template",
            "template_name": template_name,
            "si_id": si_id,
            "si_data": si_data
        })
        doc.insert(ignore_permissions=True)
        frappe.db.commit()

        return {
            "status": "success",
            "message": _("Shipping Instruction Template created."),
            "data": {
                "name": doc.name,
                "template_name": doc.template_name
            }
        }
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Create SI Template Error")
        return {"status": "error", "message": str(e)}







@frappe.whitelist(allow_guest=True)
def list_si_templates(limit_start=0, limit_page_length=10):
    try:
        limit_start = int(limit_start)
        limit_page_length = int(limit_page_length)

        templates = frappe.get_all(
            "Shipping Instruction Template",
            fields=["name", "template_name", "si_id", "creation", "modified"],
            order_by="modified desc",
            limit_start=limit_start,
            limit_page_length=limit_page_length
        )

        total_count = frappe.db.count("Shipping Instruction Template")

        return {
            "status": "success",
            "message": _("Fetched Shipping Instruction Templates successfully."),
            "limit_start": limit_start,
            "limit_page_length": limit_page_length,
            "total_count": total_count,
            "data": templates,
        }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "List SI Templates Error")
        return {"status": "error", "message": str(e)}





@frappe.whitelist(allow_guest=True)
def fetch_si_template_by_id(template_name):
    if not template_name:
        return {"status": "error", "message": _("Template id is required.")}

    try:
        doc = frappe.get_doc("Shipping Instruction Template", template_name)

        return {
            "status": "success",
            "message": _("Shipping Instruction Template fetched successfully."),
            "data": {
                "name": doc.name,
                "template_name": doc.template_name,
                "si_id": doc.si_id,
                "si_data": doc.si_data or "{}",
                "creation": doc.creation,
                "modified": doc.modified
            }
        }

    except frappe.DoesNotExistError:
        return {"status": "error", "message": _("Template not found.")}
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Fetch SI Template Error")
        return {"status": "error", "message": str(e)}




@frappe.whitelist(allow_guest=True)
def delete_si_template(template_name):
    if not template_name:
        return {"status": "error", "message": _("Template id is required.")}

    try:
        doc = frappe.get_doc("Shipping Instruction Template", template_name)
        doc.delete()
        frappe.db.commit()

        return {
            "status": "success",
            "message": _("Shipping Instruction Template deleted successfully.")
        }

    except frappe.DoesNotExistError:
        return {"status": "error", "message": _("Template not found.")}
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Delete SI Template Error")
        return {"status": "error", "message": str(e)}
    

@frappe.whitelist(allow_guest=True)
def list_shipping_instructions(limit_start=0, limit_page_length=20):
    try:
        limit_start = int(limit_start)
        limit_page_length = int(limit_page_length)
        current_user = frappe.session.user

        shipping_instructions = frappe.get_all(
        "Shipping Instructions",
        fields=[
            "name", "shipment_type", "si_name", "carrier", "carrier_booking_number",
            "shipper_reference_number", "forwarder", "bl_no", "vessel", "voyage","shipper_tax_id",
            "port_of_load_name", "port_of_discharge_name", "custom_shipper_name", "shipper",
            "booking_request_id.booking_status", "creation", "modified","owner"
        ],
        order_by="modified desc",
        limit_start=limit_start,
        limit_page_length=limit_page_length
        )

        for si in shipping_instructions:
            if si.get("carrier"):
                carrier_name = si["carrier"]
                carrier_part_name = frappe.db.get_value("Carrier", carrier_name, "partyname1")
                frappe.logger().info(f"Carrier: {carrier_name}, Part Name: {carrier_part_name}")
                si["carrier_part_name"] = carrier_part_name if carrier_part_name else None
            else:
                si["carrier_part_name"] = None

                    


        instruction_names = [i["name"] for i in shipping_instructions]

        inttra_status_data = frappe.get_all(
            "Inttra Status SI",
            filters={"shipping_instruction": ["in", instruction_names]},
            fields=["*"]
        )

        inttra_status_map = {}
        for status in inttra_status_data:
            key = status.get("shipping_instruction")
            if key:
                inttra_status_map[key] = status

        combined_list = []
        for si in shipping_instructions:
            carrier_name= si["carrier_part_name"] 
            si_name = si["name"]
            combined_list.append({
                "carrier_part_name" : si.get(carrier_name),
                "shipping_instruction": si,
                "draft_inttra": inttra_status_map.get(si_name)
            })

        total_count = frappe.db.count("Shipping Instructions")

        return {
            "status": "success",
            "message": _("Fetched Shipping Instructions and Inttra Status successfully."),
            "total_count": total_count,
            "limit_start": limit_start,
            "limit_page_length": limit_page_length,
            "data": combined_list,
            "user": current_user
        }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "List Shipping Instructions Error")
        return {"status": "error", "message": str(e)}

@frappe.whitelist(allow_guest=True)
def list_shipping_instructions_draft(limit_start=0, limit_page_length=20):
    try:
        limit_start = int(limit_start)
        limit_page_length = int(limit_page_length)
        current_user = frappe.session.user
        draft_inttra = frappe.get_all(
            "Inttra Status SI",
            filters={"submitted_status": "Draft"},
            fields=["name", "shipping_instruction", "submitted_status","inttra_si","creation", "modified"]
        )
        instruction_names = list({d["shipping_instruction"] for d in draft_inttra})

        if not instruction_names:
            return {
                "status": "success",
                "message": _("No draft Inttra Status entries found."),
                "total_count": 0,
                "data": [],
                "user": current_user
            }
        shipping_instructions = frappe.get_all(
            "Shipping Instructions",
            filters={"name": ["in", instruction_names]},
            fields=[
                "name", "si_name", "carrier",
                "vessel", "voyage",
                "port_of_load_name", "port_of_discharge_name",
                "creation", "modified","owner"
            ],
            order_by="modified desc",
            limit_start=limit_start,
            limit_page_length=limit_page_length
        )
        carrier_ids = [s["carrier"] for s in shipping_instructions if s.get("carrier")]
        carrier_map = {}
        if carrier_ids:
            carriers = frappe.get_all(
                "Carrier",
                filters={"name": ["in", carrier_ids]},
                fields=["name", "partyname1"]
            )
            carrier_map = {c["name"]: c["partyname1"] for c in carriers}

        for s in shipping_instructions:
            s["carrier_part_name"] = carrier_map.get(s.get("carrier"))
        shipping_map = {s["name"]: s for s in shipping_instructions}
        inttra_map = {}
        for entry in draft_inttra:
            si = entry["shipping_instruction"]
            if si not in inttra_map:
                inttra_map[si] = []
            inttra_map[si].append(entry)
        combined_data = []
        for si_name in shipping_map:
            combined_data.append({
                "shipping_instruction": shipping_map[si_name],
                "inttra_status_list": inttra_map.get(si_name, []),
                "carrier_part_name": shipping_map.get("carrier_part_name"),
            })

        total_count = len(instruction_names)

        return {
            "status": "success",
            "message": _("Fetched Shipping Instructions and Draft Inttra Status successfully."),
            "total_count": total_count,
            "limit_start": limit_start,
            "limit_page_length": limit_page_length,
            "data": combined_data,
            "user": current_user
        }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "List Shipping Instructions Error")
        return {"status": "error", "message": str(e)}



@frappe.whitelist()
@role_required(["Admin"])
def shipping_instructions_confirmation(name):
    try:
        if not frappe.db.exists("Shipping Instructions", name):
            return None
        
        # Get the complete shipping instruction document
        main_doc = frappe.get_doc("Shipping Instructions", name)
        data = get_doc_data(main_doc)
        
        
        latest_status = frappe.get_all(
            "Inttra Status SI",
            filters={"shipping_instruction": name},
            fields="*",
            order_by="modified desc",
            limit_page_length=1
        )

        if latest_status:
            status_data = latest_status[0]

            # Remove metadata and system fields
            meta_fields = [
                'name', 'owner', 'creation', 'modified', 'modified_by',
                'docstatus', 'idx', 'parent', 'parenttype', 'parentfield',
                '_user_tags', '_comments', '_assign', '_liked_by', '__islocal',
                '__unsaved', '__onload', '__run_link_triggers'
            ]
            for field in meta_fields:
                status_data.pop(field, None)

            data["inttra_status"] = status_data
        else:
            data["inttra_status"] = None





        
        equipment_list = frappe.get_all(
            "Equipments",
            filters={"si_id": name, "is_active": 1},
            fields=["name"],
            order_by="creation asc"
        )
        
        processed_equipment = []
        for equip in equipment_list:
            equipment_doc = frappe.get_doc("Equipments", equip.name)
            processed_equipment.append(get_doc_data(equipment_doc))

        data["equipment_list"] = processed_equipment
        
        meta = frappe.get_meta("Shipping Instructions")
        for field in meta.fields:
            if field.fieldtype == "Table" and field.fieldname not in data:
                child_data = []
                for child in main_doc.get(field.fieldname, []):
                    child_data.append(get_doc_data(child))
                data[field.fieldname] = child_data
                
        attachments = frappe.get_all(
            "File",
            filters={
                "attached_to_doctype": "Shipping Instructions",
                "attached_to_name": name
            },
            fields=["name", "file_name", "file_url", "is_private"]
        )
        data["attachments"] = attachments
        data["contract_party"] ={}

        if main_doc.contract_party:
            party_id = main_doc.contract_party
            fields = []
            party_type = None

            if party_id.startswith("CUS-"):
                party_type = "Customer DB"
                fields = [
                    "name", 
                    "customer_name as party_name",
                    "customer_country as country",
                    "phone as phone",
                    "customer_address as address",
                    "email_id as email",
                    "customer_zip as postal_code"
                ]
            elif party_id.startswith("SHI-"):
                party_type = "Shipper"
                fields = [
                    "name",
                    "shipper_name as party_name",
                    "email as email",
                    "phone as phone",
                    "custom_address as address",
                    "postal_code as postal_code",
                    "country as country"
                ]

            party_data = frappe.get_value(
                party_type,
                {"name": party_id},
                fields,
                as_dict = True                
            ) if party_type else {}

            if party_data:
                party = party_data if party_data else {}
                party["type"] = party_type.replace(" DB", "")
                data["contract_party"]= party

        elif main_doc.contract_party_name_entry:
            data["contract_party"].append({
                "party_name": main_doc.contract_party_name_entry,
                "phone": None,
                "email": None,
                "address": main_doc.contract_party_address_entry,
                "postal_code": None,
                "country": None,
                "type": "Manual"
            })




        
        return data
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "shipping_instructions_confirmation Error")
        return None

def get_doc_data(doc, cache=None, depth=0):
    """
    Recursively get all data from doc including child tables and link doctypes
    
    Args:
        doc: Document to process
        cache: Cache to avoid infinite recursion
        depth: Current recursion depth (to limit excessive nesting)
    """
    if cache is None:
        cache = {}
        
    # Prevent excessive recursion
    max_depth = 5
    if depth > max_depth:
        return {"name": doc.name, "doctype": doc.doctype, "max_depth_reached": True}
        
    # Unique key for cache to avoid infinite recursion
    doc_key = (doc.doctype, doc.name)
    if doc_key in cache:
        return cache[doc_key]
        
    # Convert to dictionary and remove metadata
    doc_data = doc.as_dict()
    remove_metadata_fields(doc_data)
    
    # Get meta for field information
    meta = frappe.get_meta(doc.doctype)
    
    # Process fields
    for fieldname in list(doc_data.keys()):
        value = doc_data[fieldname]
        if value is None or value == "":
            continue
            
       
        excluded_doctypes = ["File", "Email Queue", "Error Log"]
        
        # Get field details from meta
        field = meta.get_field(fieldname)
        if field:
            # Handle Link fields - fetch linked document data
            if field.fieldtype == "Link" and field.options not in excluded_doctypes:
                linked_doctype = field.options
                try:
                    if frappe.db.exists(linked_doctype, value):
                        # Store original value separately
                        doc_data[fieldname + "_id"] = value
                        
                        # Don't recursively process Booking Request to avoid large responses
                        if linked_doctype == "Booking Request":
                            booking_doc = frappe.get_doc("Booking Request", value)
                            # Only include essential Booking Request fields
                            doc_data[fieldname + "_data"] = {
                                "name": booking_doc.name,
                                "booking_number": booking_doc.get("booking_number", ""),
                                "booking_date": booking_doc.get("booking_date", "")
                            }
                        else:
                            linked_doc = frappe.get_doc(linked_doctype, value)
                            # Recursively fetch data from linked document
                            doc_data[fieldname + "_data"] = get_doc_data(linked_doc, cache, depth + 1)
                except (frappe.PermissionError, frappe.DoesNotExistError):
                    # Keep the ID but don't include detailed data
                    doc_data[fieldname + "_id"] = value
                    doc_data[fieldname + "_data"] = None
                    
            # Handle Table (child table) fields
            elif field.fieldtype == "Table":
                child_doctype = field.options
                child_data = []
                for child in doc.get(fieldname):
                    # Recursively fetch data from child document
                    child_data.append(get_doc_data(child, cache, depth + 1))
                doc_data[fieldname] = child_data
                
            # Handle JSON fields
            elif field.fieldtype == "JSON" and isinstance(value, str):
                try:
                    import json
                    doc_data[fieldname] = json.loads(value)
                except:
                    pass  # Keep as string if not valid JSON
            
            # Handle attachments
            elif field.fieldtype == "Attach" and value:
                doc_data[fieldname + "_url"] = frappe.utils.get_url(value)
                
            # Handle HTML and Text fields - ensure they are strings
            elif field.fieldtype in ["HTML", "Text Editor", "Small Text", "Long Text"] and value:
                doc_data[fieldname] = str(value)
    
    # Store in cache to avoid reprocessing the same document
    cache[doc_key] = doc_data
    
    return doc_data

def remove_metadata_fields(doc_data):
    """Remove Frappe-specific metadata fields."""
    metadata_fields = [
        'owner', 'creation', 'modified', 'modified_by',
        'parent', 'parentfield', 'parenttype', 'idx',
        'docstatus', 'amended_from', '_user_tags',
        '_comments', '_assign', '_liked_by', '__islocal',
        '__unsaved', '__onload', '__run_link_triggers'
    ]
    for field in metadata_fields:
        doc_data.pop(field, None)






def create_si_draft(name):
   pass




@frappe.whitelist(allow_guest=True, methods=["POST"])
def update_shipping_request():
    try:
        request_data = frappe.request.data
        if not request_data:
            return build_response({"status_code": 400, "message": "No data provided"})

        try:
            data = json.loads(request_data)
        except json.JSONDecodeError as e:
            return build_response({"status_code": 400, "message": f"Invalid JSON: {str(e)}"})

        frappe.db.begin()

        si_id = data.get("si_id")
        booking_request_id = data.get("booking_request_id")
        
        shipping_request = None

        if si_id:
            if not frappe.db.exists("Shipping Instructions", si_id):
                return build_response({"status_code": 404, "message": f"Shipping Instruction with ID {si_id} not found"})
            shipping_request = frappe.get_doc("Shipping Instructions", si_id)
        elif booking_request_id:
            existing_si = frappe.db.get_value("Shipping Instructions", 
                {"booking_request_id": booking_request_id}, "name")
            if not existing_si:
                return build_response({"status_code": 404, "message": f"No Shipping Instruction found for booking: {booking_request_id}"})
            shipping_request = frappe.get_doc("Shipping Instructions", existing_si)
        else:
            return build_response({"status_code": 400, "message": "Either si_id or booking_request_id must be provided"})

              

        consignee = data.get("consignee")
        if consignee and not frappe.db.exists("Customer DB", consignee):
            frappe.throw(f"Customer DB '{consignee}' does not exist. Please enter a valid Consignee.")

        shipper = data.get("shipper")
        if shipper and not frappe.db.exists("Shipper", shipper):
            frappe.throw(f"Shipper '{shipper}' does not exist. Please enter a valid Shipper.")

        notify_party = data.get("notify_party")
        if notify_party and not frappe.db.exists("Customer DB", notify_party):
            frappe.throw(f"Notify Party '{notify_party}' does not exist. Please enter a valid Notify Party.")        
        
        origin_place_of_carrier_receipt = data.get("origin_place_of_carrier_receipt")
        if origin_place_of_carrier_receipt and not frappe.db.exists("UNLOCODE Locations", origin_place_of_carrier_receipt):
            frappe.throw(f"UNLOCODE Locations '{origin_place_of_carrier_receipt}' does not exist. Please enter a valid Place of Carrier Receipt.")

        port_of_load = data.get("port_of_load")
        if port_of_load and not frappe.db.exists("UNLOCODE Locations", port_of_load):
            frappe.throw(f"UNLOCODE Locations '{port_of_load}' does not exist. Please enter a valid Port of Load.") 
        
        destination_place_of_carrier_delivery = data.get("destination_place_of_carrier_delivery")
        if destination_place_of_carrier_delivery and not frappe.db.exists("UNLOCODE Locations", destination_place_of_carrier_delivery):
            frappe.throw(f"UNLOCODE Locations '{destination_place_of_carrier_delivery}' does not exist. Please enter a valid Place of Carrier Delivery.")

        # Update shipping request fields exactly as in create function
        # Detail
        if data.get("bookingnumber") is not None:
            shipping_request.bookingnumber = data.get("bookingnumber")
        if data.get("creationdate") is not None:
            shipping_request.creationdate = data.get("creationdate")
        if data.get("booking_request_id") is not None:
            shipping_request.booking_request_id = data.get("booking_request_id")
        
        # Shipper details
        if data.get("shipper") is not None:
            shipping_request.shipper = data.get("shipper")
        if data.get("custom_shipper_mobile_no") is not None:
            shipping_request.custom_shipper_mobile_no = data.get("custom_shipper_mobile_no")
        if data.get("custom_shipper_name") is not None:
            shipping_request.custom_shipper_name = data.get("custom_shipper_name")
        if data.get("shipper_address") is not None:
            shipping_request.shipper_address = data.get("shipper_address")
        if data.get("shipper_email") is not None:
            shipping_request.shipper_email = data.get("shipper_email")
    
        # Forwarder Details
        if data.get("forwarder") is not None:
            shipping_request.forwarder = data.get("forwarder")
        if data.get("forwarder_address") is not None:
            shipping_request.forwarder_address = data.get("forwarder_address")

        if data.get("carrier") is not None:
            shipping_request.carrier = data.get("carrier")
        if data.get("carrier_booking_number") is not None:
            shipping_request.carrier_booking_number = data.get("carrier_booking_number")
        if data.get("carrier_email") is not None:
            shipping_request.carrier_email = data.get("carrier_email")

        # Consignee Details
        if data.get("consignee") is not None:
            shipping_request.consignee = data.get("consignee")
        if data.get("consignee__address") is not None:
            shipping_request.consignee_address = data.get("consignee__address")
        
        # Notify Party Details
        if data.get("notify_party") is not None:
            shipping_request.notify_party = data.get("notify_party")
        if data.get("additional_party_one") is not None:
            shipping_request.additional_notify_party_1 = data.get("additional_party_one")
        if data.get("additional_party_two") is not None:
            shipping_request.additional_notify_party_2 = data.get("additional_party_two")

        # Contract Party Details 
        if data.get("contract_party") is not None:
            shipping_request.contract_party = data.get("contract_party") or None
        if data.get("contract_party_address") is not None:
            shipping_request.contract_party_address = data.get("contract_party_address") if data.get("contract_party") else None
        if data.get("contract_party_name_entry") is not None:
            shipping_request.contract_party_name_entry = data.get("contract_party_name_entry") if not data.get("contract_party") else None
        if data.get("contract_party_address_entry") is not None:
            shipping_request.contract_party_address_entry = data.get("contract_party_address_entry") if not data.get("contract_party") else None

        # Freight Payer Details
        if data.get("freight_payer") is not None:
            shipping_request.freight_payer = data.get("freight_payer")
        if data.get("freight_payer_address") is not None:
            shipping_request.freight_payer_address = data.get("freight_payer_address")

        # Manufacturer/Supplier Details
        if data.get("manufacturersupplier") is not None:
            shipping_request.manufacturersupplier = data.get("manufacturersupplier")
        if data.get("manufacturersupplier_address") is not None:
            shipping_request.manufacturersupplier_address = data.get("manufacturersupplier_address")

        # Consolidator/Stuffer Details
        if data.get("consolidatorstuffer") is not None:
            shipping_request.consolidatorstuffer = data.get("consolidatorstuffer")
        if data.get("consolidatorstuffer_address") is not None:
            shipping_request.consolidatorstuffer_address = data.get("consolidatorstuffer_address")

        # Warehouse Keeper Details
        if data.get("warehouse_keeper") is not None:
            shipping_request.warehouse_keeper = data.get("warehouse_keeper")
        if data.get("warehouse_keeper_address") is not None:
            shipping_request.warehouse_keeper_address = data.get("warehouse_keeper_address")

        # Importer Details
        if data.get("importer") is not None:
            shipping_request.importer = data.get("importer")
        if data.get("importer_address") is not None:
            shipping_request.importer_address = data.get("importer_address")

        # Transport Details
        if data.get("vessel") is not None:
            shipping_request.vessel = data.get("vessel")
        if data.get("bl_no") is not None:
            shipping_request.bl_no = data.get("bl_no")
        if data.get("imo_number") is not None:
            shipping_request.imo_number = data.get("imo_number")
        if data.get("origin_place_of_carrier_receipt") is not None:
            shipping_request.origin_place_of_carrier_receipt = data.get("origin_place_of_carrier_receipt")
        if data.get("print_on_bl_asorigin") is not None:
            shipping_request.print_on_bl_asorigin = data.get("print_on_bl_asorigin")
        if data.get("shipment_type") is not None:
            shipping_request.shipment_type = data.get("shipment_type")
        if data.get("origin_name") is not None:
            shipping_request.origin_name = data.get("origin_name")
        if data.get("print_on_bl_aspol") is not None:
            shipping_request.print_on_bl_aspol = data.get("print_on_bl_aspol")
        if data.get("voyage") is not None:
            shipping_request.voyage = data.get("voyage")
        if data.get("port_of_load") is not None:
            shipping_request.port_of_load = data.get("port_of_load")
        if data.get("print_on_bl_aspod") is not None:
            shipping_request.print_on_bl_aspod = data.get("print_on_bl_aspod")
        if data.get("move_type") is not None:
            shipping_request.move_type = data.get("move_type")
        if data.get("port_of_load_name") is not None:
            shipping_request.port_of_load_name = data.get("port_of_load_name")
        if data.get("print_on_bl_asdestination") is not None:
            shipping_request.print_on_bl_asdestination = data.get("print_on_bl_asdestination")
        if data.get("door_delivery_address") is not None:
            shipping_request.door_delivery_address = data.get("door_delivery_address")
        if data.get("port_of_discharge") is not None:
            shipping_request.port_of_discharge = data.get("port_of_discharge")
        if data.get("port_of_discharge_name") is not None:
            shipping_request.port_of_discharge_name = data.get("port_of_discharge_name")
        if data.get("destination_place_of_carrier_delivery") is not None:
            shipping_request.destination_place_of_carrier_delivery = data.get("destination_place_of_carrier_delivery")
        if data.get("destination_name") is not None:
            shipping_request.destination_name = data.get("destination_name")

        # Government Tax IDs
        if data.get("shipper_tax_id") is not None:
            shipping_request.shipper_tax_id = data.get("shipper_tax_id")
        if data.get("shipper_iec") is not None:
            shipping_request.shipper_iec = data.get("shipper_iec")
        if data.get("shipper_pan_no") is not None:
            shipping_request.shipper_pan_no = data.get("shipper_pan_no")
        if data.get("notify_party_tax_id") is not None:
            shipping_request.notify_party_tax_id = data.get("notify_party_tax_id")
        if data.get("consignee_tax_id") is not None:
            shipping_request.consignee_tax_id = data.get("consignee_tax_id")

        # ICS2 Entry Summary Declaration
        if data.get("are_the_goods_going_to_be_delivered_to_these_countries") is not None:
            shipping_request.are_the_goods_going_to_be_delivered_to_these_countries = data.get("are_the_goods_going_to_be_delivered_to_these_countries")
        if data.get("do_you_prefer_the_carrier_to_declare_the_entry_summary_ens") is not None:
            shipping_request.do_you_prefer_the_carrier_to_declare_the_entry_summary_ens = data.get("do_you_prefer_the_carrier_to_declare_the_entry_summary_ens")
        if data.get("have_house_bills_been_issued_for_this_shipment") is not None:
            shipping_request.have_house_bills_been_issued_for_this_shipment = data.get("have_house_bills_been_issued_for_this_shipment")

        # Cargo Identification Numbers
        if data.get("pcin") is not None:
            shipping_request.pcin = data.get("pcin")
        if data.get("csn") is not None:
            shipping_request.csn = data.get("csn")
        if data.get("acid_number_mcin") is not None:
            shipping_request.acid_number_mcin = data.get("acid_number_mcin")

        # Particulars
        if data.get("i_am_shipping_only_one_cargo_in_this_shipment") is not None:
            shipping_request.i_am_shipping_only_one_cargo_in_this_shipment = data.get("i_am_shipping_only_one_cargo_in_this_shipment")
        if data.get("total_number_of_containers") is not None:
            shipping_request.total_number_of_containers = data.get("total_number_of_containers")
        if data.get("total_number_of_packages") is not None:
            shipping_request.total_number_of_packages = data.get("total_number_of_packages")

        # Control Totals
        if data.get("total_shipment_weight") is not None:
            shipping_request.total_shipment_weight = data.get("total_shipment_weight")
        if data.get("total_shipment_volume") is not None:
            shipping_request.total_shipment_volume = data.get("total_shipment_volume")

        # Shipper's Declared Value
        if data.get("currency_type") is not None:
            shipping_request.currency_type = data.get("currency_type")
        if data.get("shippers_declared_value") is not None:
            shipping_request.shippers__declared_value = data.get("shippers_declared_value")
    
        # B/L Preferences
        if data.get("bl_release_office_location") is not None:
            shipping_request.bl_release_office__location = data.get("bl_release_office_location")
            shipping_request.print_on_bl_aslocation = data.get("bl_release_office_location")
        if data.get("requested_date_of_issue") is not None:
            shipping_request.requested_date_of_issue = data.get("requested_date_of_issue")
        if data.get("bill_type") is not None:
            shipping_request.bill_type = data.get("bill_type")

        # B/L Print Instructions
        if data.get("freightedno_of_documents") is not None:
            shipping_request.freightedno_of_documents = data.get("freightedno_of_documents")
        if data.get("unfreightedno_of_documents") is not None:
            shipping_request.unfreightedno_of_documents = data.get("unfreightedno_of_documents")
        if data.get("non_negotiable_freightedno_of_copies") is not None:
            shipping_request.non_negotiable_freightedno_of_copies = data.get("non_negotiable_freightedno_of_copies")
        if data.get("non_negotiable_unfreighted_no_of_copies") is not None:
            shipping_request.non_negotiable_unfreighted_no_of_copies = data.get("non_negotiable_unfreighted_no_of_copies")
        if data.get("bl_comments") is not None:
            shipping_request.bl_comments = data.get("bl_comments")

        # Notification Emails and References
        if data.get("si_requestor_emails") is not None:
            shipping_request.si_requestor_emails = data.get("si_requestor_emails")
        if data.get("partner_notification_emails") is not None:
            shipping_request.partner_notification_emails = data.get("partner_notification_emails")
        if data.get("si_name") is not None:
            shipping_request.si_name = data.get("si_name")
        if data.get("shipper_reference_number") is not None:
            shipping_request.shipper_reference_number = data.get("shipper_reference_number")
        if data.get("forwarder_reference_number") is not None:
            shipping_request.forwarder_reference_number = data.get("forwarder_reference_number")
        if data.get("transaction_number") is not None:
            shipping_request.transaction_number = data.get("transaction_number")
        if data.get("unique_consignment_reference") is not None:
            shipping_request.unique_consignment_reference = data.get("unique_consignment_reference")
        if data.get("purchase_order_number") is not None:
            shipping_request.purchase_order_number = data.get("purchase_order_number")
        if data.get("contract_reference_number") is not None:
            shipping_request.contract_reference_number = data.get("contract_reference_number")
        if data.get("bl_reference_number") is not None:
            shipping_request.bl_reference_number = data.get("bl_reference_number")
        if data.get("exporter_reference_number") is not None:
            shipping_request.exporter_reference_number = data.get("exporter_reference_number")
        if data.get("consignee_order_number") is not None:
            shipping_request.consignee_order_number = data.get("consignee_order_number")
        if data.get("invoice_reference_number") is not None:
            shipping_request.invoice_reference_number = data.get("invoice_reference_number")
        if data.get("letter_of_credit_reference") is not None:
            shipping_request.letter_of_credit_reference = data.get("letter_of_credit_reference")
        if data.get("lcr_issue_date") is not None:
            shipping_request.lcr_issue_date = data.get("lcr_issue_date")
        if data.get("lcr_expiry_date") is not None:
            shipping_request.lcr_expiry_date = data.get("lcr_expiry_date")
        if data.get("customs_house_broker_reference") is not None:
            shipping_request.customs_house_broker_reference = data.get("customs_house_broker_reference")
        if data.get("government_reference_or_fmc_number") is not None:
            shipping_request.government_reference_or_fmc_number = data.get("government_reference_or_fmc_number")
        if data.get("export_license_number") is not None:
            shipping_request.export_license_number = data.get("export_license_number")
        if data.get("export_license_issue_date") is not None:
            shipping_request.eln_issue_date = data.get("export_license_issue_date")
        if data.get("export_license_expiry_date") is not None:
            shipping_request.eln_expiry_date = data.get("export_license_expiry_date")
        if data.get("house_bill_number") is not None:
            shipping_request.house_bill_number = data.get("house_bill_number")
        if data.get("si_customer_name_entry") is not None:
            shipping_request.si_customer_name_entry = data.get("si_customer_name_entry")
        if data.get("si_customer_address_entry") is not None:
            shipping_request.si_customer_address_entry = data.get("si_customer_address_entry")
        if data.get("si_notify_party_name_entry") is not None:
            shipping_request.si_notify_party_name_entry = data.get("si_notify_party_name_entry")
        if data.get("si_notify_party_address_entry") is not None:
            shipping_request.si_notify_party_address_entry = data.get("si_notify_party_address_entry")
        if data.get("additional_party_one_name_entry") is not None:
            shipping_request.additional_party_one_name_entry = data.get("additional_party_one_name_entry")
        if data.get("additional_party_one_address") is not None:
            shipping_request.additional_party_one_address = data.get("additional_party_one_address")
        if data.get("additional_party_two_name_entry") is not None:
            shipping_request.additional_party_two_name_entry = data.get("additional_party_two_name_entry")
        if data.get("additional_party_two_address") is not None:
            shipping_request.additional_party_two_address = data.get("additional_party_two_address")
        if data.get("documentation_clauses") is not None:
            shipping_request.documentation_clauses = data.get("documentation_clauses")
        if data.get("user_defined_clauses") is not None:
            shipping_request.user_defined_clauses = data.get("user_defined_clauses")
        

        # Freight Charges - child table
        if isinstance(data.get("freight_charges"), list):
            shipping_request.freight_charges = []
            for charge in data["freight_charges"]:
                shipping_request.append("freight_charges", {
                    "charge_type": charge.get("charge_type"),
                    "payer": charge.get("payer"),
                    "freight_term": charge.get("freight_term"),
                    "payment_location": charge.get("payment_location"),
                    "type": charge.get("type"),
                })

        shipping_request.message_status = "Amendment"
        try:
            current_version = float(shipping_request.doc_version) if shipping_request.doc_version else 1.0
            new_version = round(float(current_version) + 1.0, 1)
            shipping_request.doc_version = str(new_version)
        except (TypeError, ValueError):
            shipping_request.doc_version = "2.0"

        shipping_request.flags.ignore_permissions = True
        

        shipping_request.save()

        if "container" in data:
            if not isinstance(data.get("container"), list):
                frappe.throw("Container must be a list of dictionaries")

            booking_id = data.get("booking_request_id")
            if not booking_id:
                frappe.throw("Missing booking_request_id")

            # Fetch all existing equipment for the booking
            existing_equipments = frappe.get_all(
                "Equipments",
                filters={"booking_request": booking_id, "si_id": shipping_request.name},
                fields=["name", "equipment_name"]
            )
            
            eqp_names = []
            dct_eqp_name_map = {}
            lst_non_eqp_names = []
            for e in existing_equipments:
                eqp_names.append(e["equipment_name"])
                if e["equipment_name"]:
                    dct_eqp_name_map[e["equipment_name"]] = e["name"]
                else:
                    lst_non_eqp_names.append(e["name"])

            # Fallback values from first container's first cargo
            first_cargo = data.get("container", [])[0].get("cargo", [])[0] if data.get("container") and data["container"][0].get("cargo") else {}
            global_hs_code = first_cargo.get("hs_code", "").strip()
            global_description = first_cargo.get("cargo_description", "").strip()

            for item in data.get("container", []):
                if not isinstance(item, dict):
                    continue

                container_number = item.get("container_number")
                if not container_number:
                    continue

                if container_number in eqp_names:
                    eqp_names.pop(eqp_names.index(container_number))

                equipment_values = {
                    "si_id": shipping_request.name,
                    "booking_request": booking_id,
                    "equipment_name": container_number,
                    "container_type_id": item.get("container_type"),
                    "comment": item.get("comment"),
                    "supplier_type": item.get("container_supplier"),
                    "service_type": item.get("service_type"),
                    "weight_value": item.get("weight_value"),
                    "weight_type": item.get("weight_type"),
                    "wood_declaration": item.get("wood_declaration"),
                    "carrier_seal_number": item.get("carrier_seal_numbers"),
                    "shipper_seal_number": item.get("shipper_seal_numbers"),
                    "tare_weight": item.get("container_tare_weight"),
                    "cargo_weight": item.get("cargo_weight"),
                    "gross_weight": item.get("gross_weight"),
                    "doc_version": str(new_version),
                    "message_status": "Amendment",
                    "count": 1,
                    "inttra_booking_number": data.get("bookingnumber"),
                    "is_active": 1,
                }

                equipment_id = dct_eqp_name_map.get(container_number)

                if equipment_id:
                    equipment_doc = frappe.get_doc("Equipments", equipment_id)
                    equipment_doc.update(equipment_values)
                    equipment_doc.save(ignore_permissions=True)

                elif lst_non_eqp_names:
                    equipment_id = lst_non_eqp_names.pop(0)
                    equipment_doc = frappe.get_doc("Equipments", equipment_id)
                    equipment_doc.update(equipment_values)
                    equipment_doc.save(ignore_permissions=True)

                else:
                    equipment_doc = frappe.get_doc({"doctype": "Equipments", **equipment_values})
                    equipment_doc.insert(ignore_permissions=True)
                

                equipment_doc.set("cargo", [])

                for cargo in item.get("cargo", []):
                    if not isinstance(cargo, dict):
                        continue

                    hs_code = global_hs_code or cargo.get("hs_code", "").strip() 
                    description = global_description or cargo.get("cargo_description", "").strip() 

                    cargo_entry = cargo.copy()
                    cargo_entry["hs_code"] = hs_code
                    cargo_entry["cargo_description"] = description

                    equipment_doc.append("cargo", cargo_entry)

                equipment_doc.save(ignore_permissions=True)
                
                if eqp_names:
                    for eqpn in eqp_names:
                        frappe.db.set_value("Equipments", {"equipment_name": eqpn}, "is_active", 0)
            frappe.db.commit()
            try:
                record_si_version_history(shipping_request, remarks="SI amended by user")
            except Exception as e:
                frappe.log_error(frappe.get_traceback(), "Record SI Version History Error")

        xml_content = generate_shipping_instruction_xml(shipping_request.name)
        return {
            "xml_content": xml_content,
            "status_code": 200, 
            "message": "Shipping Instruction updated successfully",
            "data": shipping_request.name
        }

    except Exception as e:
        frappe.db.rollback()
        frappe.log_error(frappe.get_traceback(), "Shipping Instruction API Error")
        return {"status_code": 500, "message": f"Error: {str(e)}"}
    



# @frappe.whitelist(allow_guest=True)
# def search_carrier(carrier_booking_number):
#     try:
#         if not carrier_booking_number:
#             frappe.throw(_("Missing required parameter: carrier_booking_number"), frappe.MandatoryError)

#         booking_request = frappe.get_value("Booking Request", {"carrier_booking_number": carrier_booking_number}, "name")
#         if not booking_request:
#             frappe.local.response["http_status_code"] = 404
#             return {
#                 "status": "not found",
#                 "status_code": 404,
#                 "message": f"No data found for carrier_booking_number: {carrier_booking_number}",
#                 "data": {}
#             }

#         main_carriage_rows = frappe.get_all(
#             "Booking Main Carriage",
#             filters={"parent": booking_request},
#             fields=["port_of_load", "port_of_discharge", "vessel", "voyage"]
#         )

#         def get_unlocode_details(code):
#             if not code:
#                 return None
#             doc = frappe.get_doc("UNLOCODE Locations", code)
#             return {
#                 "name": doc.name,
#                 "locode": doc.locode,
#                 "country_code": doc.country_code,
#                 "country": doc.country,
#                 "location_name": doc.location_name,
#             }

#         carriage = []
#         for row in main_carriage_rows:
#             carriage.append({
#                 "port_of_load_code": row.port_of_load,
#                 "port_of_load_details": get_unlocode_details(row.port_of_load),
#                 "port_of_discharge_code": row.port_of_discharge,
#                 "port_of_discharge_details": get_unlocode_details(row.port_of_discharge),
#                 "vessel": row.vessel,
#                 "voyage": row.voyage
#             })

#         equipment_details = get_equipment_details(booking_request)

#         return {
#             "status": "success",
#             "message": "Details fetched successfully",
#             "status_code": 200,
#             "carrier_booking_number": carrier_booking_number,
#             "booking_id": booking_request,
#             "main_carriage": carriage,
#             "equipments": equipment_details.get("data") if equipment_details.get("status") == "success" else [],
#         }

#     except frappe.MandatoryError as e:
#         frappe.local.response["http_status_code"] = 400
#         return {
#             "status": "error",
#             "status_code": 400,
#             "message": str(e)
#         }

#     except Exception as e:
#         frappe.log_error(frappe.get_traceback(), "Error in search_by_carrier_booking_number")
#         frappe.local.response["http_status_code"] = 500
#         return {
#             "status": "error",
#             "message": "An unexpected error occurred during the search."
#         }



@frappe.whitelist(allow_guest=True)
def search_carrier(carrier_booking_number):
    try:
        if not carrier_booking_number:
            frappe.throw(_("Missing required parameter: carrier_booking_number"), frappe.MandatoryError)

        booking_id = frappe.get_value("Booking Request", {"carrier_booking_number": carrier_booking_number}, "name")
        if not booking_id:
            frappe.local.response["http_status_code"] = 404
            return {
                "status": "Not Found",
                "status_code": 404,
                "message": f"No data found for carrier_booking_number: {carrier_booking_number}",
                "data": {}
            }

        booking_doc = frappe.get_doc("Booking Request", booking_id)
        meta = frappe.get_meta("Booking Request")
        doc_data = booking_doc.as_dict()
        remove_metadata_fields(doc_data)

        total_containers = 0
        if hasattr(booking_doc, "containers") and booking_doc.containers:
            for container in booking_doc.containers:
                total_containers += cint(container.number_of_containers or 0)
        doc_data["total_number_of_containers"] = total_containers

        for fieldname in list(doc_data.keys()):
            value = doc_data[fieldname]
            if not value:
                continue

            field = meta.get_field(fieldname)
            if not field:
                continue

            if field.fieldtype == "Link":
                linked_doctype = field.options
                if linked_doctype != "Booking Request" and frappe.db.exists(linked_doctype, value):
                    try:
                        linked_doc = frappe.get_doc(linked_doctype, value)
                        linked_data = linked_doc.as_dict()
                        remove_metadata_fields(linked_data, linked_doctype)
                        doc_data[fieldname] = linked_data
                    except frappe.PermissionError:
                        doc_data[fieldname] = None

            elif field.fieldtype == "Table":
                child_data = []
                for child in booking_doc.get(fieldname):
                    child_dict = child.as_dict()
                    remove_metadata_fields(child_dict, child.doctype)
                    child_meta = frappe.get_meta(child.doctype)

                    for child_fieldname, child_value in list(child_dict.items()):
                        if not child_value:
                            continue
                        child_field = child_meta.get_field(child_fieldname)
                        if child_field and child_field.fieldtype == "Link":
                            linked_doctype = child_field.options
                            if frappe.db.exists(linked_doctype, child_value):
                                try:
                                    linked_doc = frappe.get_doc(linked_doctype, child_value)
                                    linked_data = linked_doc.as_dict()
                                    remove_metadata_fields(linked_data, linked_doctype)
                                    child_dict[child_fieldname] = linked_data
                                except frappe.PermissionError:
                                    child_dict[child_fieldname] = None
                    child_data.append(child_dict)
                doc_data[fieldname] = child_data

        movement_value = booking_doc.move_type or ""
        simplified_move_type = {
            "Port, Ramp, or CY to Port, Ramp, or CY": "PortToPort",
            "Door to Port, Ramp, or CY": "DoorToPort",
            "Door to Door": "DoorToDoor",
            "Port, Ramp, or CY to Door": "PortToDoor"
        }.get(movement_value, "")

        move_type_map = {
            "PortToPort": "Port,Ramp,CY/CFS to Port,Ramp,CY/CFS",
            "DoorToPort": "Door to Port,Ramp,CY/CFS",
            "DoorToDoor": "Door To Door",
            "PortToDoor": "Port,Ramp,CY/CFS to Door"
        }
        doc_data["move_type"] = move_type_map.get(simplified_move_type, "")

        doc_data["vessel"] = ""
        doc_data["port_of_load"] = {}
        doc_data["port_of_discharge"] = {}

        if booking_doc.main_carriage:
            main_carriage = booking_doc.main_carriage[0]
            doc_data["vessel"] = main_carriage.vessel or ""

            if main_carriage.port_of_load:
                try:
                    port_of_load_meta = frappe.get_doc("UNLOCODE Locations", main_carriage.port_of_load).as_dict()
                    remove_metadata_fields(port_of_load_meta, "UNLOCODE Locations")
                    doc_data["port_of_load"] = port_of_load_meta
                except frappe.DoesNotExistError:
                    doc_data["port_of_load"] = {}

            if main_carriage.port_of_discharge:
                try:
                    port_of_discharge_meta = frappe.get_doc("UNLOCODE Locations", main_carriage.port_of_discharge).as_dict()
                    remove_metadata_fields(port_of_discharge_meta, "UNLOCODE Locations")
                    doc_data["port_of_discharge"] = port_of_discharge_meta
                except frappe.DoesNotExistError:
                    doc_data["port_of_discharge"] = {}

        doc_data["origin_location_name"] = frappe.db.get_value(
            "UNLOCODE Locations", booking_doc.place_of_carrier_receipt, "location_name"
        ) or ""

        doc_data["destination_location_name"] = frappe.db.get_value(
            "UNLOCODE Locations", booking_doc.place_of_carrier_delivery, "location_name"
        ) or ""

        doc_data["carrier"] = {}
        if booking_doc.booking_agent:
            try:
                agent = frappe.get_doc("Carrier", booking_doc.booking_agent)
                doc_data["carrier"] = {
                    "name": agent.name,
                    "partyalias": agent.partyalias,
                    "partyname1": agent.partyname1,
                    "inttra_id": agent.inttra_id,
                    "address": agent.address,
                    "postal_code": agent.postal_code,
                    "country_code": agent.country_code,
                }
            except frappe.DoesNotExistError:
                pass

        doc_data["equipments"] = []
        equipment_names = frappe.get_all("Equipments", filters={"booking_request": booking_id}, pluck="name")
        for name in equipment_names:
            eq = frappe.get_doc("Equipments", name)
            eq_data = eq.as_dict()
            remove_metadata_fields(eq_data)

            for fname, value in eq_data.items():
                if not value:
                    continue
                field = frappe.get_meta(eq.doctype).get_field(fname)
                if field and field.fieldtype == "Link":
                    try:
                        if frappe.db.exists(field.options, value):
                            linked_doc = frappe.get_doc(field.options, value).as_dict()
                            remove_metadata_fields(linked_doc, field.options)
                            eq_data[fname] = linked_doc
                    except frappe.PermissionError:
                        eq_data[fname] = None

                elif isinstance(value, list):
                    cleaned_list = []
                    for child in eq.get(fname):
                        child_data = child.as_dict()
                        remove_metadata_fields(child_data, child.doctype)
                        for cfname, cvalue in list(child_data.items()):
                            if cvalue:
                                cfield = frappe.get_meta(child.doctype).get_field(cfname)
                                if cfield and cfield.fieldtype == "Link":
                                    try:
                                        if frappe.db.exists(cfield.options, cvalue):
                                            linked_doc = frappe.get_doc(cfield.options, cvalue).as_dict()
                                            remove_metadata_fields(linked_doc, cfield.options)
                                            child_data[cfname] = linked_doc
                                    except frappe.PermissionError:
                                        child_data[cfname] = None
                        cleaned_list.append(child_data)
                    eq_data[fname] = cleaned_list

            doc_data["equipments"].append(eq_data)

        doc_data["creationdate"] = frappe.utils.now_datetime()

        frappe.local.response["http_status_code"] = 200
        return {
            "status": "success",
            "status_code": 200,
            "message": "Data fetched successfully",
            "data": doc_data
        }

    except frappe.MandatoryError as e:
        frappe.local.response["http_status_code"] = 400
        return {
            "status": "error",
            "status_code": 400,
            "message": str(e)
        }
    except frappe.DoesNotExistError as e:
        frappe.local.response["http_status_code"] = 404
        return {
            "status": "error",
            "status_code": 404,
            "message": "Document not found: " + str(e)
        }
    except frappe.PermissionError:
        frappe.local.response["http_status_code"] = 403
        return {
            "status": "error",
            "status_code": 403,
            "message": "Permission denied"
        }
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Error in search_carrier")
        frappe.local.response["http_status_code"] = 500
        return {
            "status": "error",
            "status_code": 500,
            "message": "An unexpected error occurred"
        }


def remove_metadata_fields(doc_data, doctype=None):
    metadata_fields = [
        'owner', 'creation', 'modified', 'modified_by',
        'parent', 'parentfield', 'parenttype', 'idx',
        'docstatus', 'amended_from', '_user_tags',
        '_comments', '_assign', '_liked_by',
        'doctype', 'booking_agent', 'booking_office', 'contract_number',
    ]
    if doctype == "UNLOCODE Locations":
        metadata_fields += ['sub_division', 'function', 'status', 'date', 'coordinates']

    for field in metadata_fields:
        doc_data.pop(field, None)

    # for key in list(doc_data.keys()):
    #     if doc_data[key] in [None, ""]:
    #         doc_data.pop(key)

def record_si_version_history(shipping_request, remarks="SI amended", file_url=None):
    try:
        current_snapshot = shipping_request.as_dict()

        previous_doc = frappe.get_last_doc(
            "SI Version History",
            filters={"shipping_instruction": shipping_request.name},
            order_by="creation desc"
        ) if not remarks.lower().startswith("initial") else None

        changed_fields = {}
        old_values = {}
        new_values = {}

        if previous_doc:
            prev_snapshot = json.loads(previous_doc.si_snapshot or "{}")
            for key, new_val in current_snapshot.items():
                old_val = prev_snapshot.get(key)
                if old_val != new_val:
                    changed_fields[key] = {"old": old_val, "new": new_val}
                    old_values[key] = old_val
                    new_values[key] = new_val

        frappe.get_doc({
            "doctype": "SI Version History",
            "booking_request_id": shipping_request.booking_request_id,
            "carrier_booking_number": shipping_request.carrier_booking_number,
            "shipping_instruction": shipping_request.name,
            "doc_version": shipping_request.doc_version,
            "message_status": shipping_request.message_status,
            "amended_from_si": None,
            "amendment_date": now(),
            "changed_by": frappe.session.user,
            "remarks": remarks,
            "xml_file": file_url,
            "si_snapshot": json.dumps(current_snapshot, default=str),
            "changed_fields": json.dumps(changed_fields, default=str),
            "previous_values": json.dumps(old_values, default=str),
            "new_values": json.dumps(new_values, default=str)
        }).insert(ignore_permissions=True)

    except Exception:
        frappe.log_error(frappe.get_traceback(), "Failed to record SI version history with diff")

