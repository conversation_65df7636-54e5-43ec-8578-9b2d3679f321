import json
import frappe
from frappe import _
from frappe.utils.file_manager import save_file
from westside.www.Authentication.auth_decorators import role_required


@frappe.whitelist()
@role_required(["Vendor"])
def get_all_jobs(status=None, search=None, page=1, page_size=10):
    try:
        try:
            page = int(page)
            page_size = int(page_size)
        except ValueError:
            frappe.response["http_status_code"] = 400
            frappe.response["message"] = {"error": "Invalid page or page_size value. Must be an integer."}
            return

        if page < 1 or page_size < 1:
            frappe.response["http_status_code"] = 400
            frappe.response["message"] = {"error": "Page and page_size must be greater than zero."}
            return
        
        vendor = frappe.session.user # current user
        vendor_id = frappe.get_value("Vendor", {"email_id": vendor}, "name")

        filters = {}
        filters["vendor_name"] = vendor_id
        if status:
            filters["status"] = status 
        
        offset = (page - 1) * page_size

        # Search
        or_filters = []
        if search:
            search_term = f"%{search}%"
            or_filters.append(["booking_id", "like", search_term])
            or_filters.append(["name", "like", search_term])
            or_filters.append(["port_of_origin", "like", search_term])

        # Fetch jobs with pagination
        jobs_list = frappe.get_all(
            "Job",
            filters=filters,
            or_filters=or_filters, 
            fields=[
                "name", "booking_id", "no_of_containers", "port_of_origin", "port_cut_of_date",
                "doc_cut_of_date", "barge_cut_of_date", "status"
            ],
            limit_start=offset,
            limit_page_length=page_size
        )

    
        response_data = []
        for job in jobs_list:

            if job.booking_id:
                booking_doc = frappe.get_doc("Booking Request", job.booking_id)

            port_of_origin = None
            if job.port_of_origin:
                port_of_origin = frappe.get_value(
                    "UNLOCODE Locations",
                    job.port_of_origin,
                    ["name", "locode", "country_code", "country", "location_name"],
                    as_dict=True
                )

            try:
                job_data = {
                    "id": job.name,
                    "booking_id": job.booking_id,
                    "carrier_booking_number": booking_doc.carrier_booking_number,
                    "port_of_origin": port_of_origin,
                    "port_cut_of_date": job.port_cut_of_date,
                    "doc_cut_of_date": job.doc_cut_of_date,
                    "barge_cut_of_date": job.barge_cut_of_date,
                    "status": job.status,
                    "si_due_date": booking_doc.si_due_date,
                    "vgm_due_date": booking_doc.vgm_due_date
                }
                response_data.append(job_data)

                no_of_containers = 0
                container_list = frappe.get_all(
                        "Booking Container",
                        filters={"parent": job.booking_id},
                        fields=["number_of_containers"]
                )
                no_of_containers = sum(int(container["number_of_containers"]) for container in container_list)
                job_data['no_of_containers'] = no_of_containers

            except Exception as job_error:
                frappe.log_error(f"Error processing job {job.name}: {str(job_error)}", "Job Processing Error")

        total_count = len(frappe.get_all(
                    "Job",
                    filters=filters,
                    or_filters=or_filters,
                    fields=["name"]
                ))
        total_pages = (total_count + page_size - 1) // page_size 

        frappe.response["message"] = {
            "total_count": total_count,
            "page": page,
            "page_size": page_size,
            "total_pages": total_pages,
            "jobs": response_data
        }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Get All Jobs API Error")
        frappe.response["http_status_code"] = 500
        frappe.response["message"] = {"error": str(e)}



@frappe.whitelist(allow_guest=True)
@role_required(["Vendor"])
def get_job_detail(name=None):
    if not name:
        return { 'status_code': 400, "message": "Missing Job Id"}
    
    try:
        job_doc = frappe.get_doc("Job", name)

        if not job_doc:
            return { 'status_code': 404, "message": "Job not found"}
        
        try:
            bill_doc = frappe.get_doc("Bill", {"job_id": job_doc.name, "is_active": True})
        except frappe.DoesNotExistError:
            bill_doc = None

        if job_doc.status == "Open":
            job_doc.status = "In Progress"
            job_doc.save(ignore_permissions=True)
            frappe.db.commit()

        vendor = frappe.session.user # current user 
        vendor_doc = frappe.get_doc("Vendor", {"email_id": vendor})
       
        if job_doc.vendor_name!= vendor_doc.name:
            return { 'status_code': 403, "message": "Unauthorized to access this job"}

        port_of_origin = None
        if job_doc.port_of_origin:
            port_of_origin = frappe.get_value(
                "UNLOCODE Locations",
                job_doc.port_of_origin,
                ["name", "locode", "country_code", "country", "location_name"],
                as_dict=True
            )

        if job_doc.booking_id:
            booking_doc = frappe.get_doc("Booking Request", job_doc.booking_id)

            
        # Base result fields
        result = {
            "job_name": job_doc.name,
            "booking_id": job_doc.booking_id,
            "carrier_booking_number": booking_doc.carrier_booking_number,
            "vendor_name": vendor_doc.vendor_name,
            "no_of_containers": job_doc.no_of_containers,
            "status": job_doc.status,
            "port_of_origin": port_of_origin,
            "port_cut_of_date": job_doc.port_cut_of_date,
            "doc_cut_of_date": job_doc.doc_cut_of_date,
            "barge_cut_of_date": job_doc.barge_cut_of_date,
            "si_due_date": booking_doc.si_due_date,
            "vgm_due_date": booking_doc.vgm_due_date,
            "bill_id": bill_doc.name if bill_doc else None
        }

        hs_code = ""
        hs_code_description = ""
        if job_doc.hs_code:
            hs_code_doc = frappe.get_doc("HS Code", job_doc.hs_code)
            hs_code = hs_code_doc.hs_code
            hs_code_description = hs_code_doc.hs_code_description
        result["hs_code"] = hs_code
        result["hs_code_description"] = hs_code_description


        # FUll Url
        site_url = frappe.utils.get_url()
    
        # Container Goods Image (child table)
        container_goods_data = []
        for row in job_doc.container_goods_image:
            full_image_url = row.container_image if row.container_image else ""
            file_name = row.file_name if row.file_name else "file_name.jpg"
            container_goods_data.append({
                "image": full_image_url,
                "file_name": file_name
            })
        result["container_goods_image"] = container_goods_data

        # Uploaded pic of BL
        local_bl = site_url + job_doc.local_bl if job_doc.local_bl else ""
        local_bl_file_name = job_doc.local_bl_file_name if job_doc.local_bl_file_name else "filename"
        result["uploaded_pic_of_BL"] = local_bl
        result["BL_filename"] = local_bl_file_name

        return {
            "status_code": 200,
            "data": result
        }

    except frappe.DoesNotExistError:
        return {
            "status_code": 404,
            "message": "Job not found or not authorised to view this job."
        }

    except Exception as e:
        frappe.log_error(f"Error in get_job_detail: {str(e)}")
        return {
            "status_code": 500,
            "message": f"Internal Server Error: {str(e)}"
        }



@frappe.whitelist(allow_guest=True)
# @role_required(["Vendor"])
def get_assigned_containers(name=None):
    if not name:
        return {
            "status_code": 422,
            "message": "Booking Details Not Available or No Booking Data Found for the User"
        }

    try:
        booking_request = frappe.get_doc('Booking Request', name)
        
        # get the number of containers requested to intrra.
        no_of_containers = 0
        container_list = frappe.get_all(
                "Booking Container",
                filters={"parent": booking_request.name},
                fields=["number_of_containers"]
        )
        no_of_containers = sum(int(container["number_of_containers"]) for container in container_list)

        result = {
            'name': booking_request.name,
            'no_of_containers': no_of_containers,
        }

        equipments = frappe.get_all('Equipments', 
                        filters={'booking_request': booking_request.name, "is_active": True}, 
                        fields=[
                            'name', 'equipment_name', 'code_value', 'shipper_seal_number', 'carrier_seal_number',
                            'description', 'supplier_type', 'service_type','weight_value', 'weight_type', 'job',
                            'tare_weight', 'cargo_weight', 'gross_weight'
                        ],
                        order_by='creation desc'
                    )

        job_count = len([e for e in equipments if e.get('job')])
        result['number_of_assigned_containers'] = job_count
        
        result['equipments'] = []

        # Attach container goods images and cargo to each equipment
        for equipment in equipments:
            container_goods = frappe.get_all('Container goods image', 
                filters={'parent': equipment['name'], 'parenttype': 'Equipments'},
                fields=['container_image', 'file_name']) 

            vendor = frappe.session.user # current user
            vendor_id = frappe.get_value("Vendor", {"email_id": vendor}, "name")

            if equipment.job:
                job_doc = frappe.get_doc("Job", equipment.job)
                
                if job_doc.vendor_name == vendor_id:
                    equipment_images = []
                    for row in container_goods:
                        full_image_url = row.container_image if row.container_image else ""
                        file_name = row.file_name if row.file_name else "file_name.jpg"
                        equipment_images.append({
                            "image": full_image_url, "file_name": file_name
                            })
                     
                    # Attach images to the equipment
                    equipment['container_goods_images'] = equipment_images
                    equipment['is_my_job'] = True
                    equipment['is_assigned_to_others'] = False
                else:
                    equipment['is_my_job'] = False
                    equipment['is_assigned_to_others'] = True

                    if job_doc.vendor_name:
                        equipment['assigned_vendor_name'] = job_doc.vendor_name
                        equipment['assigned_vendor_name'] = frappe.get_value(
                            "Vendor",
                            job_doc.vendor_name,
                            ["name", "vendor_name"],
                            as_dict=True
                        )
            else:
                equipment['is_my_job'] = False
                equipment['is_assigned_to_others'] = False

        result["equipments"] = equipments

        return {
            "status_code": 200,
            "message": "Booking Request fetched successfully",
            "data": result
        }

    except frappe.DoesNotExistError:
        return {
            "status_code": 404,
            "message": "Booking Request not found"
        }
    except Exception as e:
        frappe.log_error(f"Error in get_assigned_containers: {str(e)}")
        return {
            "status_code": 500,
            "message": f"Internal Server Error: {str(e)}"
        }

