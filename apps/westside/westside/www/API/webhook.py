
import json
import frappe
from datetime import datetime

from westside.westside.doctype.booking_request.booking_request import create_new_booking, parse_date_field, safe_text_formate, update_booking_request


def authenticate_user(username, password):
    """
    Authenticate user based on username and password and get the authentication token for all api access.
    
    """
    try:
        user = frappe.db.get_value("User", {"name": username, "enabled": 1}, "name")
        if not user:
            return False

        if not frappe.auth.check_password(user, password):
            return False

        return True
    except Exception:
        return False

@frappe.whitelist(allow_guest=True)
def receive_inttra_webhook():
    """
    Receive INTTRA webhook and process the response.
    """
    try:
        username = frappe.local.request.headers.get("Username")
        password = frappe.local.request.headers.get("Password")

        if not username or not password or not authenticate_user(username, password):
            frappe.local.response["status_code"] = 401  
            frappe.local.response["message"] = "Unauthorized access, invalid username or password."
            return{
                "status_code": 401,
                "message": "Unauthorized access, invalid username or password"
            }
            
        response_data = frappe.local.request.get_json()      

        booking_id = frappe.get_value("Booking Request", {"inttra_reference": response_data.get('inttraReference')}, "name")
        doc = frappe.get_doc({
            "doctype": "Inttra API Logs",
            "description": 'Booking confirmation response',
            'booking_id': booking_id,
            'inttra_refference_id': response_data.get('inttraReference'),
            'booking_status': response_data.get('bookingState'),
            "response": json.dumps(response_data) if response_data else ""
        })
        doc.insert(ignore_permissions=True)  
        frappe.db.commit()

        processed_resp = process_inttra_booking_response(response_data, booking_id)
        if processed_resp.get("status_code") != 200:
            frappe.local.response["status_code"] = processed_resp.get("status_code")
            frappe.local.response["message"] = processed_resp.get("message")
            return processed_resp
        frappe.local.response["status_code"] = 200
        frappe.local.response["message"] = "Webhook processed successfully."
        

    except Exception as e:
        frappe.log_error(f"Error processing INTTRA webhook: {str(e)}")
        frappe.local.response["status_code"] = 500
        frappe.local.response["message"] = f"Error processing webhook: {str(e)}"
        return {
            "status_code": 500,
            "message": f"Error processing webhook: {str(e)}"
        }



@frappe.whitelist()
def process_inttra_booking_response(response_data,booking_id):
    """
    Process the INTTRA booking response and update the booking request.
    """
    try:
        if booking_id:
            try:
                save_previous_booking_data(booking_id)
            except:
                pass
            
            frappe.db.delete("Cargo", filters={"parent": booking_id, "parentfield": "cargo"})

            booking_doc = frappe.get_doc("Booking Request", booking_id)

            for party in response_data.get('parties',[]):
                if party.get('partyRole') == "BookingOffice":
                    filtered_contacts = [
                        contact for contact in booking_doc.additional_contacts
                        if contact.partner_role != "Booking Office"
                    ]
                    booking_doc.set("additional_contacts", filtered_contacts)
                    address_data = party.get("address", {})
                    str_address = None
                    str_address = ", ".join(
                            str(v) for k, v in address_data.items()
                            if k != "country" and isinstance(v, (str, int, float))
                        )
                    contact = party.get("contacts", [{}])[0] if party.get("contacts") else {}
                    str_contact_name = contact.get("name")
                    str_phones = ", ".join(contact.get("phones", []))[:140]
                    str_faxes = ", ".join(contact.get("faxes", []))[:140]

                    # Example Doctype mapping
                    booking_doc.append("additional_contacts", {
                        "partner_role" : "Booking Office",
                        "partner_name": party.get("partyName1"),
                        "contact_name": str_contact_name,
                        "telephone": str_phones,
                        "fax": str_faxes,
                        "address_line": str_address
                    })


            for package in response_data.get("packageDetails") or []:
                booking_doc.append("cargo", {
                    "container_number": "",
                    "package_counttype_outermost": frappe.get_value("Package Type", {"edi_factcode": package.get("typeValue")}, "name"),
                    "package_count": package.get("count"),
                    "hs_code": package.get("goodsClassificationValue") or 400400,
                    "origin_of_goods": "",
                    "cargo_description": package.get("goodsDescription"),
                    "package_description": package.get("typeDescription"),
                    "schedule_b_number": package.get("goodsClassificationSchedBValue"),
                    "cus_code": package.get("goodsClassificationValue") or 400400,
                    "cargo_gross_weight": (package.get("goodsGrossWeight") or {}).get("weightValue"),
                    "net_weight": (package.get("goodsNetWeight") or {}).get("weightValue"),
                    "net_weight_unit": (package.get("goodsNetWeight") or {}).get("weightType"),
                    "gross_volume": (package.get("goodsGrossVolume") or {}).get("volumeValue"),
                    "gross_volume_unit": (package.get("goodsGrossVolume") or {}).get("volumeType"),
                    "print_on_bl_as": "",
                    "ncm_codes": "",
                    "marks_and_numbers": ", ".join(package.get("marksAndNumbers", [])) if package.get("marksAndNumbers") else None
                })

            
            filtered_contacts = [
                contact for contact in booking_doc.additional_contacts
                if contact.partner_role != "Transaction Contact"
            ]
            booking_doc.set("additional_contacts", filtered_contacts)
            
            if (response_data.get("transactionContact") or {}):
                str_telephone = ", ".join((response_data.get("transactionContact") or {}).get("phones")) if (response_data.get("transactionContact") or {}).get("phones") else None
                str_email = ", ".join((response_data.get("transactionContact") or {}).get("emails")) if (response_data.get("transactionContact") or {}).get("emails") else None

                booking_doc.append("additional_contacts", {
                    "partner_role" : "Transaction Contact",
                    "partner_name" : (response_data.get("transactionContact") or {}).get("name"),
                    "contact_name" : (response_data.get("transactionContact") or {}).get("name"),
                    "telephone": str_telephone[0:140] if str_telephone else None,
                    "email" : str_email[0:140] if str_email else None,
                    "address_line": None
                })

            booking_doc.set("additional_reference", [])
            for refe in response_data.get('references',{}):
                booking_doc.append ("additional_reference",{
					"reference_type": refe.get("referenceType"),
  					"text":refe.get("referenceValue")
				}
                )
            
                
            booking_doc.booking_response_type = response_data.get("bookingResponseType", None)
            booking_doc.cargo_hazardous_indicator = response_data.get("cargoHazardousIndicator", None)
            booking_doc.cargo_controlled_environment_indicator = response_data.get("cargoControlledEnvironmentIndicator", None)
            booking_doc.cargo_environmental_pollutant_indicator = response_data.get("cargoEnvironmentalPollutantIndicator", None)
            booking_doc.cargo_out_of_gauge_indicator = response_data.get("cargoOutofGaugeIndicator", None)

            if isinstance (response_data.get("siDueDate"),dict):
                try: 
                    si_date = parse_date_field(response_data.get("siDueDate")) if response_data.get("siDueDate") else None
                except:
                    si_date = None
            else:
                si_date = None
            
            if isinstance(response_data.get("vgmDueDate"),dict):
                try:
                    vgm_date = parse_date_field(response_data.get("vgmDueDate")) if response_data.get("vgmDueDate") else None
                except:
                    vgm_date = None
            else:
                vgm_date = None
            

            update_fields = {
                "parent_carrier_booking_number": response_data.get("carrierReferenceNumber", None),
                "si_due_date": si_date,
                "vgm_due_date": vgm_date,
                "product_channel": response_data.get("channel"),
                "booking_status": response_data.get("bookingState"),
                "carrier_booking_number": response_data.get("carrierReferenceNumber"),
                "split": True if response_data.get("split") else None,
                "general_comments": (
                    "\n".join(response_data.get("generalComments"))
                    if isinstance(response_data.get("generalComments"), list)
                    else response_data.get("generalComments")
                )
            }

            update_fields = {k: v for k, v in update_fields.items() if v is not None}

            booking_doc.update(update_fields)
            booking_doc.save(ignore_permissions=True)
            frappe.db.commit()

            if response_data.get("transportLegs"):
                update_booking_carriage = update_booking_request((response_data.get("transportLegs") or []), booking_id,(response_data.get("transactionLocations") or []))
                if not update_booking_carriage:
                    return "Error updating booking carriages."
            container_summary_map = {}  # key = sizeCodeValue, value = count
            cy_date_map = {}
            doc_container_type_map = {}
            if response_data.get("equipments") or []:
                for container in response_data["equipments"]:
                    size_code_value = container.get("equipmentSizeCode", {}).get("sizeCodeValue")
                    size_code_desc = container.get("equipmentSizeCode", {}).get("sizeCodeDescription")
                    count = int(container.get("count", 0))

                    if size_code_value:
                        container_summary_map.setdefault(size_code_value, {"count": 0, "desc": size_code_desc})
                        container_summary_map[size_code_value]["count"] += count

                    try:
                        if container.get('haulage', {}).get("points"):
                            for point in container['haulage']['points']:
                                if point.get("haulageParty", {}).get("partyRole") == "FullDropOFF":
                                    for date in point.get("dates", []):
                                        if date.get("haulageDateType") == "ClosingDate":
                                            cy_date_map[size_code_value] = parse_date_field(date)
                    except:
                        pass
            # lst_container_id = frappe.db.get_all("Booking  Container", {"parent": booking_id}, pluck="name")
            for typecode, summary in container_summary_map.items():
                doc_container_type = frappe.get_value(
                    "Container Type", {"typecode": typecode},
                    ["name", "shortdescription", "typecode","groupcode"], as_dict=True
                )
                doc_container_type_map[typecode] = doc_container_type or {}

                doc_container_type_name = doc_container_type_map[typecode].get("name", "")
                str_group_code = doc_container_type_map[typecode].get("groupcode", "")
                doc_container_type_description = doc_container_type_map[typecode].get("shortdescription", "")

                booking_container_name = frappe.get_value("Booking Container", {
                    "parent": booking_id,
                    "container_type_code": typecode,
                }, "name")

                if (not booking_container_name) and len(container_summary_map) == 1:
                    booking_container_name = frappe.get_value("Booking Container", {
                        "parent": booking_id,
                        "container_group_code": str_group_code,
                    }, "name")
                # elif (not booking_container_name) and len(container_summary_map) > 1:
                #     frappe.db.delete("Booking Container", {"name": ["in", lst_container_id] })


                if booking_container_name:
                    frappe.db.set_value("Booking Container", booking_container_name, {
                        "number_of_containers": summary["count"],
                        "container_quantitytype": doc_container_type_name,
                        "container_descp": doc_container_type_description
                    })
                else:
                    doc = frappe.get_doc("Booking Request", booking_id)
                    doc.append("containers", {
                        "number_of_containers": summary["count"],
                        "container_quantitytype": doc_container_type_name,
                        "container_descp": doc_container_type_description
                    })
                    doc.save()
                frappe.db.commit()
            
            int_count = 0
            int_total_containers = 0
            index = 0
            frappe.db.set_value("Equipments", {"booking_request": booking_id}, {"is_active": 0})
            for container in response_data.get("equipments") or []:
                if int(container.get('count') or 0) > 1:
                    int_count = int_count + int(container.get('count'))

                else:
                    int_total_containers = len(response_data.get("equipments"))

                size_code = container.get("equipmentSizeCode") or {}
                net_weight = container.get("netWeight") or {}
                container_desc = (container.get("equipmentSizeCode") or {}).get("sizeCodeDescription")
                size_code_value = size_code.get("sizeCodeValue") or None
                container_name = container.get('identifierValue') or None
                haulage = container.get('haulage') or {}
                haulage_json = json.dumps(haulage) if haulage else None
                container_comments = container.get('comments')[0] if isinstance(container.get('comments'), list) and container.get('comments') else None
                
                doc_equipments = None
                booking_container_name = frappe.get_value("Booking Container", {
                        "parent": booking_id,
                        "container_descp": container_desc
                    }, "name")

                if booking_container_name:
                    frappe.db.set_value("Booking Container", booking_container_name, {
                        "container_name": container.get('identifierValue'),
                        "response_haulage_details" : json.dumps(container.get('haulage'))
                    })
                if container.get('identifierValue'):
                    doc_equipments = frappe.get_value("Equipments", {
                        "booking_request": booking_id,
                        "equipment_name": container.get('identifierValue')
                    }, "name")
                    if not doc_equipments:
                        doc_equipments = frappe.get_all("Equipments", 
                            filters={"booking_request": booking_id,"equipment_name": ""}, 
                            fields=["name"]
                        )
                        if doc_equipments:
                            doc_equipments = doc_equipments[0].get('name')
                        else:
                            doc_equipments = None
                elif not container.get('identifierValue'):
                    doc_equipments = frappe.get_all("Equipments", 
                        filters={"booking_request": booking_id}, 
                        fields=["name",'equipment_name'], order_by="creation asc"
                    )
                    """ if already containers exsisting, but getting more that count from inttra, then create new containers. This is for summ of count, like count = $ more than 1 """
                    if doc_equipments and int_total_containers and len(doc_equipments) < int_total_containers:
                        doc_equipments = None
                    # if already containers exsisting, but getting more that count from inttra, then create new containers, This is for count = 1
                    elif doc_equipments and int_count and len(doc_equipments) < int_count:
                        doc_equipments = None
                    # if already containers exsisting, but getting same count from inttra, then update existing containers, don't create new containers, This is for count = 1 or more than 1
                    elif doc_equipments:
                        doc_equipments = doc_equipments[index].get('name')
                        index = index + 1

                if doc_equipments:
                    print(doc_equipments, "doc_equipments")
                    frappe.db.set_value("Equipments", doc_equipments, {
                        "count": container.get("count"),
                        "equipment_name": container_name,
                        "inttra_booking_number": response_data.get("inttraReference"),
                        "carrier_booking_number": response_data.get("carrierReferenceNumber"),
                        "supplier_type": container.get("supplierType"),
                        "service_type": container.get("serviceType"),
                        "code_value": size_code_value,
                        "description": size_code.get("sizeCodeDescription"),
                        "weight_value": net_weight.get("weightValue"),
                        "weight_type": net_weight.get("weightType"),
                        "comment": container_comments,
                        "response_haulage_details": haulage_json,
                        "is_active": 1
                    })
                    frappe.db.commit()
                    
                elif not container_name and int(container.get('count', 1)) > 1:
                    names = frappe.get_all("Equipments", filters={"booking_request": booking_id,"equipment_name": "" , "is_active": 0},pluck="name")
                    if names:
                        frappe.db.delete("Equipments", {"name": ["in", names]})
                    for _ in range(0, int(container.get("count"))):
                        equipment = frappe.new_doc("Equipments")
                        equipment.booking_request = booking_id
                        equipment.count = 1
                        equipment.inttra_booking_number = response_data.get("inttraReference")
                        equipment.carrier_booking_number = response_data.get("carrierReferenceNumber")
                        equipment.equipment_name = container_name
                        equipment.supplier_type = container.get("supplierType")
                        equipment.service_type = container.get("serviceType")
                        equipment.code_value = size_code_value
                        equipment.description = size_code.get("sizeCodeDescription")
                        equipment.weight_value = net_weight.get("weightValue")
                        equipment.weight_type = net_weight.get("weightType")
                        equipment.comment = container_comments
                        equipment.is_active = 1
                        equipment.response_haulage_details = haulage_json
                        equipment.insert(ignore_permissions=True)
                        frappe.db.commit()
                else:
                    equipment = frappe.new_doc("Equipments")
                    equipment.booking_request = booking_id
                    equipment.count = container.get("count")
                    equipment.inttra_booking_number = response_data.get("inttraReference")
                    equipment.carrier_booking_number = response_data.get("carrierReferenceNumber")
                    equipment.equipment_name = container_name
                    equipment.supplier_type = container.get("supplierType")
                    equipment.service_type = container.get("serviceType")
                    equipment.code_value = size_code_value
                    equipment.description = size_code.get("sizeCodeDescription")
                    equipment.weight_value = net_weight.get("weightValue")
                    equipment.weight_type = net_weight.get("weightType")
                    equipment.comment = container_comments
                    equipment.is_active = 1
                    equipment.response_haulage_details = haulage_json
                    equipment.insert(ignore_permissions=True)
                   
                    frappe.db.commit()

            

            str_pending_reasons = safe_text_formate((response_data.get("pendingReason") or None))
            str_carrier_terms_and_conditions = safe_text_formate((response_data.get("carrierTermsAndConditions") or None))
            str_split_reason = safe_text_formate((response_data.get("splitDetails") or {}).get("splitReason"))
            str_split_comment = safe_text_formate((response_data.get("splitDetails") or {}).get("comments"))
            str_split_description = safe_text_formate((response_data.get("splitDetails") or {}).get("description"))
            str_pending_description = safe_text_formate(response_data.get("pendingDescription") or None)
            str_hazardous_summary = safe_text_formate(response_data.get("carrierHazardousSummary") or None)
            str_vessel_rate_of_exchange = safe_text_formate(response_data.get("vesselRateOfExchange") or None)
            str_carrier_reason_for_change = safe_text_formate(response_data.get("carrierReasonForChange") or None)
            frappe.db.set_value("Booking Request", booking_id, {
                    "cy_date": cy_date_map.get(typecode) if cy_date_map and typecode else None,
                    "pending_reasons": str_pending_reasons,
                    "pending_description": str_pending_description,
                    "carrier_hazardous_summary": str_hazardous_summary,
                    "carrier_terms_and_conditions": str_carrier_terms_and_conditions,
                    "split_reason": str_split_reason,
                    "comment": str_split_comment,
                    "description" : str_split_description,
                    "vessel_rate_of_exchange" : str_vessel_rate_of_exchange,
                    "carrier_reason_for_change" : str_carrier_reason_for_change
                })
            
            frappe.db.commit()
            frappe.response ["status_code"] = 200,
            frappe.response["message"] =  "Booking request updated successfully."
            return {
                "status_code": 200,
                "message": "Booking request updated successfully."
            }

        else:
            resp_split = create_new_booking(response_data)
            if resp_split:
                frappe.local.response ["status_code"] = 200,
                frappe.local.response["message"] =  "Booking request updated successfully."
                return {
                    "status_code": 200, 
                    "message": "Booking request updated successfully."
                }
            else:
                # frappe.local.response ["status_code"] = resp_split.get("status_code")
                # frappe.local.response["message"] =  resp_split.get("message")
                return resp_split
                
    except Exception as e:
        frappe.log_error(f"Error processing INTTRA booking response: {str(e)}")
        return {
            "status_code": 500,
            "message": f"Error processing booking response: {str(e)}"
        }
       
        
    

def save_previous_booking_data(name):
    """Save previous data for history purpose of Booking Request doc, including child tables."""
    try:
        booking_doc = frappe.get_doc("Booking Request", name)
        result = {}

        for df in booking_doc.meta.fields:
            if df.fieldtype in ("Section Break", "Column Break", "HTML", "Button") or df.fieldname.startswith(("column_break_", "section_break_")):
                continue

            if df.fieldtype == "Table":
                result[df.fieldname] = [
                    {
                        key: value
                        for key, value in d.as_dict().items()
                        if key not in ("doctype", "parent", "parenttype", "idx")
                    }
                    for d in booking_doc.get(df.fieldname)
                ]
            else:
                result[df.fieldname] = booking_doc.get(df.fieldname)

        dct_filter = {}
        if result.get("inttra_reference") and result.get("carrier_booking_number"):
            dct_filter = {
                "inttra_booking_number": result["inttra_reference"],
                "carrier_booking_number": result["carrier_booking_number"]
            }
        elif result.get("inttra_reference"):
            dct_filter = {"inttra_booking_number": result["inttra_reference"]}

        equipments_result = []
        equip_names = frappe.get_all("Equipments", filters=dct_filter, fields=["name"])
        for equip in equip_names:
            equip_doc = frappe.get_doc("Equipments", equip["name"])
            equip_data = {}

            for df in equip_doc.meta.fields:
                if df.fieldtype in ("Section Break", "Column Break", "HTML", "Button") or df.fieldname.startswith(("column_break_", "section_break_")):
                    continue

                if df.fieldtype == "Table":
                    equip_data[df.fieldname] = [
                        {
                            key: value
                            for key, value in d.as_dict().items()
                            if key not in ("doctype", "parent", "parenttype", "idx")
                        }
                        for d in equip_doc.get(df.fieldname)
                    ]
                else:
                    equip_data[df.fieldname] = equip_doc.get(df.fieldname)

            equipments_result.append(equip_data)

        result["equipments"] = equipments_result

        frappe.get_doc({
            "doctype": "Booking Request History",
            "booking_id": name,
            "previous_data": frappe.as_json(result),
            "created_date": datetime.now()
        }).insert(ignore_permissions=True)

        frappe.db.commit()
        return True

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Error in save_previous_booking_data")
        return{
            "status_code": 500,
            "message": f"Error saving previous booking data: {str(e)}"
        }


