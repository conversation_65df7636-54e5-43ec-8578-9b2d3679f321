import frappe
from frappe import _ 
from westside.www.Authentication.auth_decorators import role_required
       
         
   
@frappe.whitelist()
@role_required(["Admin"])
def get_job_detail(name=None):
    if not name:
        return { 'status_code': 400, "message": "Missing Job Id"}
    
    try:
        job_doc = frappe.get_doc("Job", name)

        if not job_doc:
            return { 'status_code': 404, "message": "Job not found"}
        
        try:
            bill_doc = frappe.get_doc("Bill", {"job_id": job_doc.name, , "is_active": True})
        except frappe.DoesNotExistError:
            bill_doc = None
        
        vendor_name = None
        if job_doc.vendor_name:
            vendor_doc = frappe.get_doc("Vendor", job_doc.vendor_name)
            vendor_name = vendor_doc.vendor_name
        
        port_of_origin = None
        if job_doc.port_of_origin:
            port_of_origin = frappe.get_value(
                "UNLOCODE Locations",
                job_doc.port_of_origin,
                ["name", "locode", "country_code", "country", "location_name"],
                as_dict=True
            )
        
        if job_doc.booking_id:
            booking_doc = frappe.get_doc("Booking Request", job_doc.booking_id)
    
            all_jobs = frappe.get_all(
                "Job",      
                filters={"booking_id": job_doc.booking_id},
                fields="vendor_name"
            )
            # get other assigned vendors.
            vendors = []
            for vendor in all_jobs:
                if vendor.vendor_name and vendor.vendor_name != job_doc.vendor_name:
                    vendor_data = frappe.get_value("Vendor", vendor.vendor_name, ['vendor_name'])
                    vendors.append(vendor_data)
            
        # Base result fields
        result = {
            "job_name": job_doc.name,
            "vendor_name": vendor_name,
            "booking_id": job_doc.booking_id,
            "carrier_booking_number": booking_doc.carrier_booking_number,
            "no_of_containers": job_doc.no_of_containers,
            "status": job_doc.status,
            "port_of_origin": port_of_origin,
            "port_cut_of_date": job_doc.port_cut_of_date,   # In frontend, used vgm_due_date for this.
            "doc_cut_of_date": job_doc.doc_cut_of_date,     # In frontend, used si_due_date for this.
            "barge_cut_of_date": job_doc.barge_cut_of_date, # In frontend, used vgm_due_date for this.
            "si_due_date": booking_doc.si_due_date,
            "vgm_due_date": booking_doc.vgm_due_date,
            "other_vendors": vendors,
            "bill_id": bill_doc.name if bill_doc else None
        }

        # HS Code
        hs_code = ""
        hs_code_description = ""
        if job_doc.hs_code:
            hs_code_doc = frappe.get_doc("HS Code", job_doc.hs_code)
            hs_code = hs_code_doc.hs_code
            hs_code_description = hs_code_doc.hs_code_description
        result["hs_code"] = hs_code
        result["hs_code_description"] = hs_code_description
    
        # Container Goods Image (child table)
        container_goods_data = []
        for row in job_doc.container_goods_image:
            full_image_url = row.container_image if row.container_image else ""
            file_name = row.file_name if row.file_name else "file_name.jpg"
            container_goods_data.append({
                "image": full_image_url, "file_name": file_name
            })
        result["container_goods_image"] = container_goods_data

        # Uploaded pic of BL
        local_bl = job_doc.local_bl if job_doc.local_bl else ""
        local_bl_file_name = job_doc.local_bl_file_name if job_doc.local_bl_file_name else "filename"
        result["uploaded_pic_of_BL"] = local_bl
        result["BL_filename"] = local_bl_file_name

        # Bill
        result["bill"] = {}
        bill_name = frappe.get_value("Bill", {"job_id": name, , "is_active": True}, "name")
        if bill_name:
            bill_doc = frappe.get_doc("Bill", bill_name) 
            result["bill"] = {
                "id": bill_doc.name,
                "bill_number": bill_doc.bill_number,
                "bill_date": bill_doc.bill_date,
                "amount": bill_doc.amount,
                "weight": bill_doc.weight,
                "tax": bill_doc.tax,
                "job": bill_doc.job,
            }

        return {
            "status_code": 200,
            "data": result
        }

    except frappe.DoesNotExistError:
        return {
            "status_code": 404,
            "message": "Job not found"
         }

    except Exception as e:
        frappe.log_error(f"Error in get_job_detail: {str(e)}")
        return {
            "status_code": 500,
            "message": f"Internal Server Error: {str(e)}"
        }


