import frappe       
from frappe import _      
import json
from westside.www.Authentication.auth_decorators import role_required



@frappe.whitelist()
@role_required(["Admin"])
def prepare_docket_creation_data(carrier_booking_number):
    try:
        if not carrier_booking_number:
            return {"status": 400, "message": "Carrier Booking Number is required"}

        booking_name = frappe.get_value("Booking Request", {"carrier_booking_number": carrier_booking_number}, "name")
        if not booking_name:
            return {"status": 400, "message": f"No Booking Request found with carrier_booking_number :{carrier_booking_number}"}

        booking = frappe.get_doc("Booking Request", booking_name)
        if not booking:
            return {"status": 400, "message": "Booking request not found."}
        try:
            bill_of_lading_doc = frappe.get_doc("Bill of Lading", {"carrier_booking_number": booking.carrier_booking_number})
        except:
            return {"status": 400, "message": "Bill of ladding not found for this booking."}

        if not bill_of_lading_doc:
            return {"status": 400, "message": "Bill of ladding not found."}
        
        data = {
            "name": bill_of_lading_doc.name,
            "carrier_booking_number": bill_of_lading_doc.carrier_booking_number,
            "bol_number": bill_of_lading_doc.bol_number,
            "shipment_id": bill_of_lading_doc.shipment_id,
            "contract_number": bill_of_lading_doc.contract_number,
            "shipped_on_board_date": bill_of_lading_doc.shipped_on_board_date,
            "port_of_load": bill_of_lading_doc.port_of_load_location,
            "port_of_discharge": bill_of_lading_doc.port_of_discharge_location,
            "carrier": bill_of_lading_doc.carrier,
            "weight": bill_of_lading_doc.total_gross_weight,
            "hs_code": bill_of_lading_doc.hs_code,
            "material": bill_of_lading_doc.hs_code_description
        }

        # Specific fields from child tables
        data["references"] = [
            {
                "reference_type": ref.reference_type,
                "text": ref.text
            } for ref in bill_of_lading_doc.references
        ]

        data["parties"] = [
            {
                "partner_role": party.partner_role,
                "partner_name": party.partner_name,
                "partner_identifier": party.partner_identifier,
                "contact_name": party.contact_name,
                "telephone": party.telephone,
                "email": party.email,
                "address_line": party.address_line,

            } for party in bill_of_lading_doc.parties
        ]

        shipper = None
        shipper_telephone = None
        shipper_contact = None
        shipper_address = None
        shipper_name = None

        consignee_address = None
        consignee_name = None
        consignee_contact = None
        consignee_telephone = None
        
        for party in data.get("parties", []):
            if party.get("partner_role") == "Shipper":
                shipper_telephone = party.get("telephone")
                shipper_name = party.get("partner_name")
                shipper_contact = party.get("contact_name")
                shipper_address = party.get("address_line")

            if party.get("partner_role") == "Consignee":
                consignee_name = party.get("partner_name")
                consignee_address = party.get("address_line")
                consignee_contact = party.get("contact_name")
                consignee_telephone = party.get("telephone")

        
        # origin of goods
        if booking.main_carriage:
            main_carriages = frappe.get_all(
                "Booking Main Carriage",
                filters={"parent": booking.name}, 
                fields=["name", "etd"],
                order_by="creation asc",
                limit=1
            )
           

        # Origin : main_carriage[0].port_of_load
        origin_of_goods = None
        if main_carriages:
            main_carriage_doc = frappe.get_doc("Booking Main Carriage", main_carriages[0].name)
            if main_carriage_doc.port_of_load:
                origin = frappe.get_value(
                    "UNLOCODE Locations", 
                    main_carriage_doc.port_of_load,
                    ["name", "locode", "country_code", "country", "location_name"],
                    as_dict=True
                )
            origin_of_goods = origin["country"] if origin and origin.get("country") else None
        
        # Containers --> booking containers --> container type ( fetch the types counts )
        if booking.containers:
            continers_dict = {}
            for container in booking.containers:
                if container.container_quantitytype:

                    continer_type_doc = frappe.get_doc("Container Type", container.container_quantitytype)

                    if continer_type_doc.typecode in continers_dict:
                        continers_dict[continer_type_doc.typecode] += container.number_of_containers
                    else:
                        continers_dict[continer_type_doc.typecode] = container.number_of_containers
            
            containers_result = ", ".join(f"{count}X{type_code}" for type_code, count in continers_dict.items())
        
        return {
            "status": "success",
            "data": {
                "booking_id": booking.name,
                "customer": consignee_name,
                "consignee_address": consignee_address,
                "shipper": shipper_address,
                "shipper_name": shipper_name,
                "hs_code" :  data["hs_code"],
                "material": data["material"],
                "shipping_date": data["shipped_on_board_date"],
                "origin": data["port_of_load"],
                "destination": data["port_of_discharge"],
                "bill_no" :  data['bol_number'],
                "destination_contact" : consignee_contact,
                "telephone" : consignee_telephone,
                "origin_of_goods" : origin_of_goods,
                "weight": data["weight"],
                "contact": shipper_contact,
                "containers":containers_result if containers_result else None,
                "shipline": data["carrier"]
            }
        }
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "get_booking_request_error")
        frappe.throw(_("An unexpected error occurred: ") + str(e))



@frappe.whitelist()
@role_required(["Admin"])
def create_docket():
    try:  
        carrier_booking_number = frappe.request.args.get("carrier_booking_number")

        if not carrier_booking_number:
            return {'status': 400, 'message': 'Missing Carrier booking number.'}

        if frappe.request.data:
            data = json.loads(frappe.request.data)
        else:
            frappe.response['http_status_code'] = 400
            frappe.response["message"] = {"error": "Invalid JSON payload."}
            return 

        booking_name = frappe.get_value("Booking Request", {"carrier_booking_number": carrier_booking_number}, "name")
        if not booking_name:
            return {"status": 400, "message": f"No Booking Request found with carrier_booking_number :{carrier_booking_number}"}

        booking_doc = frappe.get_doc("Booking Request", booking_name)

        if not booking_doc:
            return {'status': 404, 'message': 'Booking request not found'}

        si_doc_name = frappe.get_value("Shipping Instructions", {"carrier_booking_number": carrier_booking_number}, "name")
        if not si_doc_name:
            return {"status": 400, "message": f"No Shipping Instructions found with carrier_booking_number :{carrier_booking_number}"}

        si_doc = frappe.get_doc("Shipping Instructions", si_doc_name)

        if not si_doc:
            return {'status': 404, 'message': 'Shipping Instructions not found'}


        # Check for existing Docket with same booking and customer
        existing_docket = frappe.db.exists(
            "Docket DB",
            {"booking": booking_doc.name})

        if existing_docket:
            return {
                "status": 409,
                "message": f"Docket already exists with Carrier booking number '{booking_doc.carrier_booking_number}' and Customer '{data.get("customer")}'.",
                "existing_docket_id": existing_docket
            }
                   
        booking = booking_doc
        customer = data.get("customer")
        customer_id = si_doc.consignee  # Customer id is taking from Shipping Instruction.
        shipper = data.get("shipper")
        shipper_name = data.get("shipper_name")
        consignee = data.get("consignee")
        hs_code = data.get("hs_code")
        material = data.get("material")
        shipping_date = data.get("shipping_date")
        invoice = data.get("invoice")
        origin = data.get("origin")
        destination = data.get("destination")
        blno = data.get("blno")
        destination_contact = data.get("destination_contact")
        telephone = data.get("telephone")
        terms = data.get("terms")
        origin_of_goods = data.get("origin_of_goods")
        weight = data.get("weight")
        contact = data.get("contact")
        containers = data.get("containers")
        shipline = data.get("shipline")
        country_of_import_export = data.get("country_of_import_export")

        if not customer:
            frappe.response["http_status_code"] = 400
            frappe.response["message"] = {"error": "customer name is required."}
            return

        # create customer doc
        docket = frappe.get_doc({
            "doctype": "Docket DB",
            "booking": booking.name,
            "customer": customer,
            "customer_id": customer_id,
            "shipper_name": shipper_name,
            "shipper": shipper,
            "consignee": consignee,
            "material": material,
            "shipping_date": shipping_date,
            "invoice": invoice,
            "hs_code": hs_code,
            "origin": origin,
            "destination": destination,
            "blno": blno,
            "destination_contact": destination_contact,
            "telephone": telephone,
            "terms": terms,
            "origin_of_goods": origin_of_goods,
            "weight": weight,
            "contact": contact,
            "containers": containers,
            "shipline": shipline,
            "status": "New",
            "country_of_import_export": country_of_import_export
        })

        docket.insert(ignore_permissions=True)
        docket.save(ignore_permissions=True)
        frappe.db.commit()
        
        return {
            "status": 200,
            "message": "Docket created successfully",
            "docket_id": docket.name
        }
        
    except Exception as e:
        frappe.db.rollback()
        frappe.log_error(frappe.get_traceback(), "Create Docket API Error")
        return {
            "status": 500,
            "message": {"error": str(e)}
        }


@frappe.whitelist()
@role_required(["Admin"])
def get_containers(carrier_booking_number=None):
    if not carrier_booking_number:
        return {
            "status_code": 422,
            "message": "Carrier Booking Number is required."
        }

    try:
        booking_name = frappe.get_value("Booking Request", {"carrier_booking_number": carrier_booking_number}, "name")
        if not booking_name:
            return {"status": 400, "message": f"No Booking Request found with carrier_booking_number :{carrier_booking_number}"}

        booking_request = frappe.get_doc("Booking Request", booking_name)

        result = {
            'name': booking_request.name,
        }

        equipments = frappe.get_all('Equipments', 
                filters={'booking_request': booking_request.name, "is_active": True}, 
                fields=[
                    'name', 'equipment_name', 'code_value', 'shipper_seal_number',
                    'supplier_type', 'service_type'
                ], 
                order_by='creation desc'
            )

        equipments_data = []
        total_net_weight = 0  

        for eq in equipments:
            # Get the Cargo for this equipment
            cargo = frappe.get_value(
                'Cargo',
                filters={'parent': eq.name, 'parenttype': 'Equipments'},
                fieldname=[
                    'container_number', 'package_count', 'package_counttype_outermost', 'hs_code',
                    'cargo_description', 'cargo_gross_weight', 'net_weight', 'net_weight_unit'
                ],
                as_dict=True 
            )
            
            # Add cargo info to equipment
            net_weight = cargo.net_weight if cargo and cargo.net_weight else None
            if net_weight:
                total_net_weight += net_weight

            eq["cargo"] = cargo or {}
            eq["net_weight"] = net_weight
            eq["cargo_description"] = cargo.cargo_description if cargo and cargo.cargo_description else ""
            eq["package_count"] = cargo.package_count if cargo and cargo.package_count else ""
            eq["package_type"] = cargo.package_counttype_outermost if cargo and cargo.package_counttype_outermost else ""

            equipments_data.append(eq)

        # Final result
        result['number_of_assigned_containers'] = len(equipments_data)
        result['equipments'] = equipments_data
        result['net_weight'] = total_net_weight

        site_url = frappe.utils.get_url()

        return {
            "status_code": 200,
            "message": "Booking Request fetched successfully",
            "data": result
        }

    except frappe.DoesNotExistError:
        return {
            "status_code": 404,
            "message": "Booking Request not found"
        }
    except Exception as e:
        frappe.log_error(f"Error in get_assigned_containers: {str(e)}")
        return {
            "status_code": 500,
            "message": f"Internal Server Error: {str(e)}"
        }


@frappe.whitelist()
@role_required(["Admin"])
def edit_container_details(equipment_id, container_number=None, seal_number=None, net_weight=None, package_type=None, package_count=None):
    try:
        if not equipment_id:
            return {
                "status_code": 422,
                "message": "Equipment ID is required."
            }

        equipment = frappe.get_doc("Equipments", equipment_id)

        if seal_number is not None:
            equipment.shipper_seal_number = seal_number

        if container_number is not None:
            equipment.equipment_name = container_number

        # equipment.save(ignore_permissions=True)

        # Get cargo row ( assuming one child row per equipment )
        cargo_row = None
        for row in equipment.cargo:
            cargo_row = row
            break

        if cargo_row:
            if net_weight is not None:
                cargo_row.net_weight = net_weight

            if package_count is not None:
                cargo_row.package_count = package_count
            
            if package_type is not None:
                cargo_row.package_counttype_outermost = package_type

            equipment.save(ignore_permissions=True)
            frappe.db.commit()

            return {
                "status_code": 200,
                "message": "Container details updated successfully."
            }
        else:
            return {
                "status_code": 404,
                "message": "No cargo entry found for this equipment."
            }

    except frappe.DoesNotExistError:
        return {
            "status_code": 404,
            "message": "Equipment not found"
        }
        
    except Exception as e:
        frappe.db.rollback()
        frappe.log_error(frappe.get_traceback(), "Edit Container Details Error")
        return {
            "status_code": 500,
            "message": f"Internal Server Error: {str(e)}"
        }
