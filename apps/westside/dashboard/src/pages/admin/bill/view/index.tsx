import TitleSection from "@/components/titleSection";
import ViewBillPage from "@/views/admins/bill/view";
import { useAuthContext } from "@/lib/providers/context/AuthContext";

const BillView = () => {
  const { role } = useAuthContext();
  return (
    <div className="p-6">
      <TitleSection
        title="Bills"
        breadcrumbs={[
          ...(role !== "Vendor"
            ? [
                {
                  text: "Vendor",
                },
              ]
            : []),
          {
            text: "Bills",
            href: "/dashboard/vendors/bills",
          },
          {
            text: "View Bill",
          },
        ]}
      />
      <div className="pt-6">
        <ViewBillPage/>
      </div>
    </div>
  )
}

export default BillView
