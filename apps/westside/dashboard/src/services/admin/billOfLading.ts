import { axiosClient } from "@/lib/axios";
import { BillOfLading } from "@/types/BillOfLading";


export const fetchBillOfLadingList = async (
//   searchKey: string,
  currentPage: any,
  // consigneeFilter: string,
  port_of_loading: string,
  carrierQuery: string,
)=> {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `method/westside.www.Web_API.get_bill_of_lading.get_bill_of_lading`;
    // Build params conditionally
    const params: Record<string, any> = { page: currentPage };
    // if (consigneeFilter) params.consignee = consigneeFilter;
    if (port_of_loading) params.port_of_loading = port_of_loading;
    if (carrierQuery) params.carrier = carrierQuery;

    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
      params,
    });
    return response.data;
  } catch (err) {
    console.error("Error fetching bill of lading list:", err);
    throw err;
  }
};


export const fetchBillOfLadingPdf = async (fileName: string) => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `method/westside.api.file_handler.serve_pdf?filename=${fileName}`;
    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
      responseType: "blob",
    });

    return response.data;
  } catch (err) {
    console.error("Error fetching bill of lading pdf:", err);
    throw err;
  }
};

export const filterConsignee = async () => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");
    const api = `method/westside.www.Web_API.get_bill_of_lading.bol_filter_consignee_data`;
    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
      
    });

    return response.data;
  } catch (err) {
    console.error("Error fetching consignee list:", err);
    throw err;
  }
};

export const filterCarrier = async (
//   searchKey: string,
//   countryFilter: string,
//   page: number
)=> {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `method/westside.www.Web_API.get_bill_of_lading.bol_filter_carrier_data`;
    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
      params: {
        // search: searchKey,
        // country: countryFilter,
        // page,
      },
    });
    return response.data;
  } catch (err) {
    console.error("Error fetching carrier list:", err);
    throw err;
  }
};


export const fetchBillOfLadingDetails = async (bolId: string) => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `method/westside.www.Web_API.get_bill_of_lading.get_details_bill_of_lading`;
    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
      params: { bol_id: bolId },
    });

    return response.data;
  } catch (err) {
    console.error("Error fetching bill of lading details:", err);
    throw err;
  }
};