import React, { useState, useEffect } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "@/components/ui/select";
import { Card } from "@/components/ui/card";
import {
  Trash2,
  PlusCircle,
  Minus,
  Save,
  Image,
  Plus,
  LucideEraser,
  X,
} from "lucide-react";
import {
  generateVendorDetails,
  fetchAllQuickBooksCategory,
  getVendors,
  fetchAllQuickBooksProducts,
  createQuickBooksBill,
  getBillDetailsFromJob,
} from "@/services/admin/billGenerate";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { ComboBox } from "@/components/ui/comboBox";
import { Controller } from "react-hook-form";
import { toast } from "sonner";
import {
  <PERSON>ert<PERSON><PERSON>og,
  AlertDialog<PERSON>rigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { useFormContext } from "react-hook-form";
import { fetchBookingLocations } from "@/services/admin/booking";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { FormProvider } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import SpinnerLoader from "@/components/Loader/SpinnerLoader";
import { Eye, ChevronsUpDown, Check } from "lucide-react";
import { Navigate, useNavigate, useSearchParams } from "react-router";
import { ComboBoxPair } from "@/components/ui/comboBox-pair";

export default function GenerateInvoicePage() {
  const {
    register,
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm({
    defaultValues: {
      vendorName: "",
      vendor_bill_no: "",
      bookingNo: "",
      hsCode: "",
      originPort: { name: "", location: "", locode: "" },
      destinationPort: { name: "", location: "", locode: "" },
      bolNo: "",
      shippingDate: "",
      dueDate: "",
      bill_date: "",
      category: [
        { name: "", description: "", amount: 0, quickbooks_category_id: "" },
      ],
      items: [
        {
          product: "",
          description: "",
          qty: 1,
          uom: "",
          rate: 0,
          quickbooks_item_id: "",
        },
      ],
      comments: "",
      customerName: "",
      billTo: "",
      email: "",
      contact: "",
      quickbooks_vendor: "",
    },
  });
  const {
    fields: itemFields,
    append: appendItem,
    remove: removeItem,
  } = useFieldArray({
    control,
    name: "items",
  });

  const {
    fields: categoryFields,
    append: appendCategory,
    remove: removeCategory,
  } = useFieldArray({
    control,
    name: "category",
  });

  const [isLoading, setIsLoading] = useState(false);

  const [attachments, setAttachments] = useState<File[]>([
    undefined as unknown as File,
  ]);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [formPayload, setFormPayload] = useState<any | null>(null);
  const [originPortQuery, setOriginPortQuery] = useState("");
  const [originPortOpen, setOriginPortOpen] = useState(false);
  const [shownOriginValue, setShownOriginValue] = useState("");

  // Destination Port state
  const [destinationPortQuery, setDestinationPortQuery] = useState("");
  const [destinationPortOpen, setPortOfDestinationOpen] = useState(false);
  const [shownDestinationValue, setShownDestinationValue] = useState("");
  const [shownValue, setShownValue] = useState("");
  const methods = useForm();
  const navigate = useNavigate();
  const handleAttachmentChange = (e, index) => {
    const file = e.target.files?.[0];
    if (!file) return;
    setAttachments((prev) => {
      const updated = [...prev];
      updated[index] = file;
      return updated;
    });
  };

  const [searchParams, setSearchParams] = useSearchParams();
  const params = new URLSearchParams(searchParams);
  const jobId = params.get("jobId");

  const { data: vendorDetails } = useQuery({
    queryKey: ["generateVendorDetails"],
    queryFn: () => generateVendorDetails(),
  });

  const [categorySearch, setCategorySearch] = useState("");
  const {
    data: quickBooksCategories,
    isLoading: quickBooksCategoriesIsLoading,
  } = useQuery({
    queryKey: ["fetchAllQuickBooksCategory", categorySearch],
    queryFn: () => fetchAllQuickBooksCategory(categorySearch),
    // enabled: !!categorySearch,
  });
  console.log(quickBooksCategories);
  // const { data: quickBooksCategories } = useQuery({
  //   queryKey: ["fetchAllQuickBooksCategory"],
  //   queryFn: () => fetchAllQuickBooksCategory(),
  // });
  const [productSearch, setProductSearch] = useState("");
  const { data: quickBooksProducts, isLoading: quickBooksProductsIsLoading } =
    useQuery({
      queryKey: ["fetchAllQuickBooksProducts", productSearch],
      queryFn: () => fetchAllQuickBooksProducts(productSearch),
      // enabled: !!productSearch,
    });
  console.log(quickBooksProducts);
  const { data: vendorsData } = useQuery({
    queryKey: ["getVendors"],
    queryFn: () => getVendors(),
  });

  const { data: originPortData, isFetching: isOriginFetching } = useQuery({
    queryKey: ["fetchOriginPortLocation", { search: originPortQuery }],
    queryFn: fetchBookingLocations,
  });
  // Destination Port API
  const { data: destinationPortData, isFetching: isDestinationFetching } =
    useQuery({
      queryKey: [
        "fetchDestinationPortLocation",
        { search: destinationPortQuery },
      ],
      queryFn: fetchBookingLocations,
    });

  const { data: BillDetailsFromJobData } = useQuery({
    queryKey: ["fetchBillDetailsFromJob", { jobId: jobId }],
    queryFn: () => getBillDetailsFromJob(jobId || ""),
    enabled: !!jobId,
  });

  const handleOriginSearch = (val: string) => {
    setOriginPortQuery(val);
  };

  const handleDestinationSearch = (val: string) => {
    setDestinationPortQuery(val);
  };
  // const { setValue, watch } = useForm(); // Add `setValue` and `watch`
  const [selectedVendor, setSelectedVendor] = useState(null);

  const docketVendorOptions =
    vendorsData?.message?.data?.vendors?.map((vendor: any) => ({
      label: vendor.vendor_name,
      value: vendor.name,
    })) || [];

  useEffect(() => {
    if (BillDetailsFromJobData?.data?.vendor_id) {
      setValue("vendorName", BillDetailsFromJobData?.data?.vendor_id);
    }
    if (BillDetailsFromJobData?.data?.carrier_booking_num) {
      setValue("bookingNo", BillDetailsFromJobData?.data?.carrier_booking_num);
    }
    if (BillDetailsFromJobData?.data?.hs_code) {
      setValue("hsCode", BillDetailsFromJobData?.data?.hs_code);
    }
    if (BillDetailsFromJobData?.data?.bol_number) {
      setValue("bolNo", BillDetailsFromJobData?.data?.bol_number);
    } 
      if (BillDetailsFromJobData?.data.port_of_origin) {
    const originPortStr = `${BillDetailsFromJobData?.data.port_of_origin.location_name}, ${BillDetailsFromJobData?.data.port_of_origin.country} (${BillDetailsFromJobData?.data.port_of_origin.locode})`;

    setValue("originPort", {
      name: BillDetailsFromJobData?.data.port_of_origin.name,
      location: originPortStr,
      locode: BillDetailsFromJobData?.data.port_of_origin.locode,
    });
    setShownOriginValue(originPortStr); // Optional: update UI display string if used
  }
  }, [BillDetailsFromJobData, setValue]);

  const selectedCustomerId = selectedVendor?.Id;
  useEffect(() => {
    if (!selectedCustomerId) return;

    const selectedCustomer =
      vendorDetails?.message?.QueryResponse?.Vendor?.find(
        (customer: any) => customer.Id === selectedCustomerId
      );

    if (selectedCustomer.PrimaryEmailAddr?.Address) {
      setValue("email", selectedCustomer.PrimaryEmailAddr?.Address);
    } else {
      setValue("email", "");
    }
    if (selectedCustomer?.BillAddr) {
      const addr = selectedCustomer.BillAddr;
      const fullAddress = `${addr.Line1 || ""}, ${addr.City || ""}, ${
        addr.CountrySubDivisionCode || ""
      } ${addr.PostalCode || ""}`;
      setValue("billTo", fullAddress.trim());
    } else {
      setValue("billTo", "");
    }

    if (selectedCustomer?.PrimaryPhone?.FreeFormNumber) {
      setValue("contact", selectedCustomer?.PrimaryPhone?.FreeFormNumber);
    } else {
      setValue("contact", "");
    }
  }, [selectedCustomerId, setValue]);

  const quickBookVendorOptions =
    vendorDetails?.message?.QueryResponse?.Vendor?.map((vendor: any) => ({
      label: vendor.DisplayName,
      value: vendor.Id,
    })) || [];

  const onSubmit = (data: any) => {
    setFormPayload(data);
    setShowConfirmDialog(true); // Show confirmation dialog
  };

  const handleConfirmSubmit = async () => {
    setIsLoading(true);
    if (!formPayload) return;

    const data = formPayload;

    // const categoryItems = data.category.map((cat: any) => ({
    //   category_name: cat.name,
    //   category_description: cat.description,
    //   amount: Number(cat.amount),
    //   quickbooks_category_id: cat?.quickbooks_category_id,
    // }));

    let categoryItems: any[] = [];

    const isOnlyEmptyCategory =
      data.category.length === 1 && !data.category[0].quickbooks_category_id;

    if (isOnlyEmptyCategory) {
      categoryItems = [{}];
    } else {
      categoryItems = data.category
        .filter((cat: any) => cat.name && cat.name.trim() !== "")
        .map((cat: any) => ({
          category_name: cat.name,
          category_description: cat.description,
          amount: Number(cat.amount),
          quickbooks_category_id: cat?.quickbooks_category_id,
        }));
    }
    let itemsDetails: any[] = [];

    const isOnlyEmptyItem =
      data.items.length === 1 && !data.items[0].quickbooks_item_id;

    if (isOnlyEmptyItem) {
      itemsDetails = [{}];
    } else {
      itemsDetails = data.items
        .filter((item: any) => item.product && item.product.trim() !== "")
        .map((item: any) => {
          const amount = Number(item.qty) * Number(item.rate);
          return {
            item_name: item.product,
            item_description: item.description,
            qty: Number(item.qty),
            rate: Number(item.rate),
            uom: item.uom || "", // Ensure uom is included
            amount,
            billable: 0,
            taxable: 0,
            quickbooks_customer_id: "",
            quickbooks_item_id: item.quickbooks_item_id,
          };
        });
    }

    const payload = {
      vendor: data.vendorName,
      vendor_bill_no: data?.vendor_bill_no,
      origin_port: data.originPort?.name || "",
      destination_port: data.destinationPort?.name || "",
      booking_id: data.bookingNo,
      bol_number: data.bolNo,
      memo: data.comments,
      quickbooks_vendor: selectedVendor || {},
      vendor_billTo: data.billTo,
      vendor_email: data.email,
      vendor_contact: data.contact,
      category_items: categoryItems,
      items_details: itemsDetails,
      shipping_date: data?.shippingDate,
      due_date: data?.dueDate,
      bill_date: data?.bill_date,
      hs_code: data?.hsCode,
      job_id: jobId || "",
      // attachments: [],
    };
    const fileDetails = attachments.filter((file) => file instanceof File);

    try {
      console.log(JSON.stringify(payload));
      const response = await createQuickBooksBill(payload, fileDetails);
      console.log("Bill Created:", response);
      if (response?.status_code === "200") {
        toast.success("Bill generated successfully!");
        setShowConfirmDialog(false);
        setFormPayload(null);
        navigate("/dashboard/bill-view/" + response?.data?.Bill?.Id + (jobId ? `?jobId=${jobId}` : ""));
      } else {
        toast.error(
          response?.message?.Message ===
            "Required param missing, need to supply the required value for the API"
            ? "Please fill all required fields."
            : response?.message?.Message || "Failed to generate bill."
        );
        setShowConfirmDialog(false);
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Failed to create bill:", error);
      toast.error("Failed to generate bill.");
      setShowConfirmDialog(false);
      setIsLoading(false);
    }
    setIsLoading(false);
  };

  return (
    <div className="p-6">
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="grid grid-cols-1 md:grid-cols-4 gap-4"
      >
        {/* Left Section */}
        <Card className="md:col-span-3 p-6 space-y-6">
          <h2 className="text-lg font-semibold text-orange-600">
            Bill Details
          </h2>

          {/* Vendor Name (Full Width) */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Vendor Name
            </label>
            <ComboBoxPair
              label=""
              value={watch("vendorName")}
              onChange={(val) => setValue("vendorName", val)}
              options={docketVendorOptions}
              placeholder="Select Vendor"
            />
          </div>

          {/* Other Fields in 3-Column Grid */}
          <div className="grid md:grid-cols-3 gap-4">
            <div className="space-y-1">
              <label className="text-sm font-medium text-gray-700">
                Vendor Bill Number
              </label>
              <Input type="text" {...register("vendor_bill_no")} />
            </div>
            <div className="space-y-1">
              <label className="text-sm font-medium text-gray-700">
                Bill Date
              </label>
              <Input type="date" {...register("bill_date")} />
            </div>
            <div className="space-y-1">
              <label className="text-sm font-medium text-gray-700">
                Booking #
              </label>
              <Input
                {...register("bookingNo")}
                // disabled={jobId ? true : false}
              />
            </div>
            <div className="space-y-1">
              <label className="text-sm font-medium text-gray-700">
                HS Code
              </label>
              <Input {...register("hsCode")} />
            </div>
            <div className="space-y-1">
              {/* <label className="text-sm font-medium text-gray-700">
                Origin Port
              </label> */}
              {/* <Input {...register("originPort")} /> */}

              <div className="">
                <FormProvider {...methods}>
                  <FormField
                    control={control}
                    name="originPort"
                    render={({ field }) => {
                      const { value, onChange } = field;

                      return (
                        <FormItem className="w-full">
                          <FormLabel className="flex justify-between items-center">
                            <span className="text-sm text-gray-700 font-medium">
                              Origin Port
                            </span>
                            {(value?.name ||
                              value?.location ||
                              value?.locode) && (
                              <span
                                className="text-sm text-muted-foreground underline cursor-pointer"
                                onClick={() => {
                                  setValue("originPort", {
                                    name: "",
                                    location: "",
                                    locode: "",
                                  });
                                  setShownOriginValue(""); // Reset UI display string too
                                }}
                              >
                                Clear
                              </span>
                            )}
                          </FormLabel>
                          <FormControl>
                            <Popover
                              open={originPortOpen}
                              onOpenChange={setOriginPortOpen}
                            >
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  className="justify-between w-full overflow-hidden h-11"
                                >
                                  {value?.name
                                    ? shownOriginValue
                                    : "Select Location..."}
                                  <ChevronsUpDown className="opacity-50" />
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-full p-0">
                                <Command>
                                  <CommandInput
                                    placeholder="Search Location..."
                                    value={originPortQuery}
                                    onValueChange={setOriginPortQuery}
                                    className="h-9"
                                  />
                                  <CommandList>
                                    <CommandEmpty>
                                      {isOriginFetching ? (
                                        <div className="flex justify-center w-full">
                                          <SpinnerLoader />
                                        </div>
                                      ) : (
                                        "No Location Found."
                                      )}
                                    </CommandEmpty>
                                    <CommandGroup>
                                      {originPortData?.message?.results?.map(
                                        (location) => {
                                          const locationStr = `${
                                            location.location_name
                                          }, ${
                                            location.sub_division
                                              ? location.sub_division + ", "
                                              : ""
                                          }${location.country} (${
                                            location.locode
                                          })`;

                                          return (
                                            <CommandItem
                                              key={location.name}
                                              value={locationStr}
                                              onSelect={() => {
                                                onChange({
                                                  name: location.name,
                                                  location: locationStr,
                                                  locode: location.locode,
                                                });
                                                setShownOriginValue(
                                                  locationStr
                                                );
                                                setOriginPortOpen(false);
                                              }}
                                            >
                                              {locationStr}
                                              <Check
                                                className={`ml-auto ${
                                                  value?.name === location.name
                                                    ? "opacity-100"
                                                    : "opacity-0"
                                                }`}
                                              />
                                            </CommandItem>
                                          );
                                        }
                                      )}
                                    </CommandGroup>
                                  </CommandList>
                                </Command>
                              </PopoverContent>
                            </Popover>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                </FormProvider>
              </div>
            </div>
            <div className="space-y-1">
              {/* <label className="text-sm font-medium text-gray-700">
                Destination Port
              </label>
              <Input {...register("destinationPort")} /> */}
              <div className="">
                <FormProvider {...methods}>
                  <FormField
                    control={control}
                    name="destinationPort"
                    render={({ field }) => {
                      const { value, onChange } = field;

                      return (
                        <FormItem className="w-full">
                          <FormLabel className="flex justify-between items-center">
                            <span className="text-sm text-gray-700 font-medium">
                              Destination Port
                            </span>
                            {(value?.name ||
                              value?.location ||
                              value?.locode) && (
                              <span
                                className="text-sm text-muted-foreground underline cursor-pointer"
                                onClick={() => {
                                  setValue("destinationPort", {
                                    name: "",
                                    location: "",
                                    locode: "",
                                  });
                                  setShownDestinationValue(""); // Clear UI display string
                                }}
                              >
                                Clear
                              </span>
                            )}
                          </FormLabel>
                          <FormControl>
                            <Popover
                              open={destinationPortOpen}
                              onOpenChange={setPortOfDestinationOpen}
                            >
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  className="justify-between w-full overflow-hidden h-11"
                                >
                                  {value?.name
                                    ? shownDestinationValue
                                    : "Select Location..."}
                                  <ChevronsUpDown className="opacity-50" />
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-full p-0">
                                <Command>
                                  <CommandInput
                                    placeholder="Search Location..."
                                    value={destinationPortQuery}
                                    onValueChange={setDestinationPortQuery}
                                    className="h-9"
                                  />
                                  <CommandList>
                                    <CommandEmpty>
                                      {isDestinationFetching ? (
                                        <div className="flex justify-center w-full">
                                          <SpinnerLoader />
                                        </div>
                                      ) : (
                                        "No Location Found."
                                      )}
                                    </CommandEmpty>
                                    <CommandGroup>
                                      {destinationPortData?.message?.results?.map(
                                        (location) => {
                                          const locationStr = `${
                                            location.location_name
                                          }, ${
                                            location.sub_division
                                              ? location.sub_division + ", "
                                              : ""
                                          }${location.country} (${
                                            location.locode
                                          })`;

                                          return (
                                            <CommandItem
                                              key={location.name}
                                              value={locationStr}
                                              onSelect={() => {
                                                onChange({
                                                  name: location.name,
                                                  location: locationStr,
                                                  locode: location.locode,
                                                });
                                                setShownDestinationValue(
                                                  locationStr
                                                );
                                                setPortOfDestinationOpen(false);
                                              }}
                                            >
                                              {locationStr}
                                              <Check
                                                className={`ml-auto ${
                                                  value?.name === location.name
                                                    ? "opacity-100"
                                                    : "opacity-0"
                                                }`}
                                              />
                                            </CommandItem>
                                          );
                                        }
                                      )}
                                    </CommandGroup>
                                  </CommandList>
                                </Command>
                              </PopoverContent>
                            </Popover>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                </FormProvider>
              </div>
            </div>
            <div className="space-y-1">
              <label className="text-sm font-medium text-gray-700">BOL #</label>
              <Input {...register("bolNo")}/>
            </div>
            <div className="space-y-1">
              <label className="text-sm font-medium text-gray-700">
                Shipping Date
              </label>
              <Input type="date" {...register("shippingDate")} />
            </div>
            <div className="space-y-1">
              <label className="text-sm font-medium text-gray-700">
                Due Date
              </label>
              <Input type="date" {...register("dueDate")} required />
            </div>
          </div>

          {/* Category Details */}
          <div className="space-y-2">
            <h3 className="font-semibold text-base text-gray-700">
              Category Details
            </h3>
            <div className="space-y-2">
              {/* Column Labels */}
              <div className="grid grid-cols-3 gap-2 px-1 text-sm font-medium text-muted-foreground">
                <span className="text-black">Category</span>
                <span className="text-black">Description</span>
                <span className="text-black">Amount</span>
              </div>

              {/* Category Inputs */}
              {categoryFields.map((field, idx) => {
                const selectedValue = watch(`category.${idx}.name`);

                return (
                  <div
                    key={field.id}
                    className="grid grid-cols-3 gap-2 items-center"
                  >
                    {/* Category Search Dropdown */}
                    <Popover>
                      <PopoverTrigger asChild>
                         <button
                            type="button"
                            className="w-full min-w-[160px] max-h-[50px] border border-gray-300 rounded-sm px-3 py-2 flex justify-between items-start text-sm text-left whitespace-normal break-words overflow-y-auto"
                          >
                            <span className="flex-1 pr-2 overflow-y-auto max-h-[30px] leading-snug">
                            {selectedValue || "Select Category"}
                          </span>
                          <ChevronsUpDown className="h-4 w-4 ml-2 opacity-50" />
                        </button>
                      </PopoverTrigger>
                      <PopoverContent className="p-0 w-full min-w-[200px]">
                        <Command>
                          <CommandInput
                            placeholder="Search category..."
                            onValueChange={(val) => setCategorySearch(val)}
                            className="h-9"
                          />
                          <CommandList>
                            <CommandEmpty>
                              {categorySearch.length === 0
                                ? "Type to search..."
                                : quickBooksCategoriesIsLoading
                                ? "Loading..."
                                : "No categories found."}
                            </CommandEmpty>
                            <CommandGroup>
                              {quickBooksCategories?.message?.map(
                                (category: any) => (
                                  <CommandItem
                                    key={category.Id}
                                    value={category.Name}
                                    onSelect={() => {
                                      setValue(
                                        `category.${idx}.name`,
                                        category.Name
                                      );
                                      setValue(
                                        `category.${idx}.description`,
                                        category.Classification || ""
                                      );
                                      setValue(
                                        `category.${idx}.quickbooks_category_id`,
                                        category.Id || ""
                                      );
                                    }}
                                  >
                                    {category.Name}
                                    {selectedValue === category.Name && (
                                      <Check className="ml-auto h-4 w-4 text-primary" />
                                    )}
                                  </CommandItem>
                                )
                              )}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>

                    {/* Description */}
                    <Input
                      {...register(`category.${idx}.description`)}
                      placeholder="Description"
                    />

                    {/* Amount and Remove */}
                    <div className="flex items-center gap-2">
                      <Input
                        type="number"
                        min={0}
                        {...register(`category.${idx}.amount`, {
                          min: {
                            value: 0,
                            message: "Amount must be 0 or greater",
                          },
                        })}
                        placeholder="0.000"
                        className="text-right appearance-none [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                        inputMode="decimal"
                      />
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeCategory(idx)}
                        type="button"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                );
              })}

              {/* Action Buttons */}
              <div className="flex items-center gap-4 text-sm mt-1">
                <Button
                  type="button"
                  onClick={() =>
                    appendCategory({
                      name: "",
                      description: "",
                      amount: 0,
                      quickbooks_category_id: "",
                    })
                  }
                  variant="ghost"
                  className="text-orange-600"
                >
                  <Plus className="w-4 h-4 text-orange-600" /> Add Item
                </Button>
                <Button
                  type="button"
                  onClick={() => {
                    removeCategory();
                    appendCategory({
                      name: "",
                      description: "",
                      amount: 0,
                      quickbooks_category_id: "",
                    });
                  }}
                  variant="ghost"
                  className="text-orange-600"
                >
                  <LucideEraser className="w-4 h-4 text-orange-600" />
                  Clear All Lines
                </Button>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="font-semibold text-base text-gray-700">
              Items Details
            </h3>

            <div className="w-full overflow-x-auto">
              <div className="min-w-[900px] space-y-2">
                {/* Column Labels */}
                <div className="grid grid-cols-[250px_300px_80px_120px_100px_120px_30px] gap-2 px-1 text-sm font-medium text-muted-foreground">
                  <span className="text-black">Product/Services</span>
                  <span className="text-black">Product Description</span>
                  <span className="text-black">Qty</span>
                  <span className="text-black">UOM</span>
                  <span className="text-black">Rate</span>
                  <span className="text-black">Amount</span>
                  <span className="text-black"></span>
                </div>

                {/* Item Rows */}
                {itemFields.map((field, index) => {
                  const qty = watch(`items.${index}.qty`) || 0;
                  const rate = watch(`items.${index}.rate`) || 0;
                  const amount = (qty * rate).toFixed(3);
                  const selectedProduct = watch(`items.${index}.product`);

                  return (
                    <div
                      key={field.id}
                      className="grid grid-cols-[250px_300px_80px_120px_100px_120px_30px] gap-2 items-center"
                    >
                      {/* Product/Service Search Dropdown */}
                      <Popover>
                        <PopoverTrigger asChild>
                          <button
  type="button"
  className="w-full min-w-[250px] max-h-[50px] border border-gray-300 rounded-sm px-3 py-2 flex justify-between items-start text-sm text-left whitespace-normal break-words overflow-y-auto"
>
  <span className="flex-1 pr-2 overflow-y-auto max-h-[30px] leading-snug">
    {selectedProduct || "Select Product"}
  </span>
                            <ChevronsUpDown className="h-4 w-4 ml-2 opacity-50" />
                          </button>
                        </PopoverTrigger>
                        <PopoverContent className="p-0 w-full min-w-[300px]">
                          <Command>
                            <CommandInput
                              placeholder="Search product..."
                              onValueChange={(val) => setProductSearch(val)}
                              className="h-9"
                            />
                            <CommandList>
                              <CommandEmpty>
                                {productSearch.length === 0
                                  ? "Type to search..."
                                  : quickBooksProductsIsLoading
                                  ? "Loading..."
                                  : "No product found."}
                              </CommandEmpty>
                              <CommandGroup>
                                {quickBooksProducts?.message?.map(
                                  (product: any) => (
                                    <CommandItem
                                      key={product.Id}
                                      value={product.Name}
                                      onSelect={() => {
                                        setValue(
                                          `items.${index}.product`,
                                          product.Name
                                        );
                                        setValue(
                                          `items.${index}.description`,
                                          product.Description || ""
                                        );
                                        setValue(
                                          `items.${index}.rate`,
                                          product.UnitPrice || 0
                                        );
                                        setValue(
                                          `items.${index}.quickbooks_item_id`,
                                          product.Id || ""
                                        );
                                        setValue(
                                          `items.${index}.uom`,
                                          product.uom || ""
                                        );
                                      }}
                                    >
                                      {product.Name}
                                      {selectedProduct === product.Name && (
                                        <Check className="ml-auto h-4 w-4 text-primary" />
                                      )}
                                    </CommandItem>
                                  )
                                )}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>

                      {/* Description */}
                      <Input
                        {...register(`items.${index}.description`)}
                        placeholder="Description"
                      />

                      {/* Quantity */}
                      <Input
                        type="number"
                        min={0}
                        {...register(`items.${index}.qty`, {
                          min: {
                            value: 0,
                            message: "Quantity must be 0 or greater",
                          },
                          setValueAs: (v) => (v === "" ? 0 : parseFloat(v)),
                        })}
                        onBlur={(e) => {
                          const val = e.target.value;
                          if (val && val !== "0") {
                            // Strip leading zeros before a digit (e.g., "01.5" becomes "1.5")
                            e.target.value = val.replace(/^0+(\d)/, "$1");
                          }
                        }}
                        placeholder="Qty"
                        className="appearance-none [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                        inputMode="decimal"
                      />

                      {/* UOM */}
                      <Controller
                        control={control}
                        name={`items.${index}.uom`}
                        render={({ field }) => (
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="UOM" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Ton">Ton</SelectItem>
                              <SelectItem value="Kgs">Kgs</SelectItem>
                              <SelectItem value="MT">MT</SelectItem>
                              <SelectItem value="Lbs">Lbs</SelectItem>
                              <SelectItem value="Container">
                                Container
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        )}
                      />

                      {/* Rate */}
                      <Input
                        type="number"
                        min={0}
                        {...register(`items.${index}.rate`, {
                          min: {
                            value: 0,
                            message: "Rate must be 0 or greater",
                          },
                          setValueAs: (v) => (v === "" ? 0 : parseFloat(v)),
                        })}
                        step="0.01"
                        onBlur={(e) => {
                          const val = e.target.value;
                          if (val && val !== "0") {
                            // Remove leading zeros before digit (preserve decimals like 0.5)
                            e.target.value = val.replace(/^0+(\d)/, "$1");
                          }
                        }}
                        placeholder="Rate"
                        className="appearance-none [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                        inputMode="decimal"
                      />
                      {/* Calculated Amount */}
                      <Input
                        readOnly
                        value={amount}
                        className="text-right"
                        placeholder="Amount"
                      />

                      {/* Remove Button */}
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeItem(index)}
                        type="button"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  );
                })}

                {/* Action Buttons */}
                <div className="flex items-center gap-4 text-sm mt-1">
                  <Button
                    type="button"
                    onClick={() =>
                      appendItem({
                        product: "",
                        description: "",
                        qty: 1,
                        uom: "",
                        rate: 0,
                        quickbooks_item_id: "",
                      })
                    }
                    variant="ghost"
                    className="text-orange-600"
                  >
                    <Plus className="w-4 h-4 text-orange-600" /> Add Item
                  </Button>
                  <Button
                    type="button"
                    onClick={() => {
                      removeItem();
                      appendItem({
                        product: "",
                        description: "",
                        qty: 1,
                        uom: "",
                        rate: 0,
                        quickbooks_item_id: "",
                      });
                    }}
                    variant="ghost"
                    className="text-orange-600"
                  >
                    <LucideEraser className="w-4 h-4 text-orange-600" />
                    Clear All Lines
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Comments */}

          <div className="space-y-1">
            <label className="text-sm font-medium text-gray-700">
              Comments
            </label>
            <Textarea {...register("comments")} placeholder="Comments..." />
          </div>
          {/* Attachments */}
          <div className="space-y-2">
            <label className="block text-sm font-medium">Attach File</label>
            {attachments.map((file, index) => (
              <div className="flex items-center gap-2" key={index}>
                <label className="flex items-center gap-2 px-3 py-2 bg-white border border-gray-300 rounded cursor-pointer">
                  <Image className="w-5 h-5" />
                  <span>CHOOSE FILE</span>
                  <input
                    type="file"
                    className="hidden"
                    onChange={(e) => handleAttachmentChange(e, index)}
                  />
                </label>
                {/* <Input value={file?.name || ""} readOnly className="flex-1" /> */}
                <div className="flex-1 flex items-center justify-between bg-gray-50 px-4 py-2 text-sm">
                  <span className="truncate text-gray-700">
                    {file?.name || "Upload Attach File Here...."}
                  </span>

                  {file && (
                    <button
                      type="button"
                      onClick={() => {
                        const updated = [...attachments];
                        updated[index] = undefined as unknown as File;
                        setAttachments(updated);
                      }}
                      className="text-gray-500 hover:text-red-600 ml-2"
                      title="Remove file"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  )}
                </div>
                {attachments.length > 1 && (
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() =>
                      setAttachments(attachments.filter((_, i) => i !== index))
                    }
                  >
                    <Minus />
                  </Button>
                )}
                {index === attachments.length - 1 && (
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() =>
                      setAttachments([
                        ...attachments,
                        undefined as unknown as File,
                      ])
                    }
                  >
                    <PlusCircle />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </Card>

        {/* Right Section */}
        <Card className="p-6 space-y-4">
          <h2 className="text-lg font-semibold text-orange-600">
            Vendor Details
          </h2>
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Vendor in Quickbooks
            </label>

            <ComboBoxPair
              label=""
              value={watch("quickbooks_vendor")}
              onChange={(val) => {
                const selected =
                  vendorDetails?.message?.QueryResponse?.Vendor?.find(
                    (v: any) => v.Id === val
                  );
                setValue("quickbooks_vendor", val);
                setSelectedVendor(selected);
              }}
              options={quickBookVendorOptions}
              placeholder="Select Vendor"
              //error={errors.customerName?.message}
            />
          </div>
          <div className="space-y-1">
            <label className="text-sm font-medium text-gray-700">Bill To</label>
            <Textarea {...register("billTo")} placeholder="Bill To" />
          </div>
          <div className="space-y-1">
            <label className="text-sm font-medium text-gray-700">
              Email Address
            </label>
            <Input {...register("email")} placeholder="Email Address" />
          </div>
          <div className="space-y-1">
            <label className="text-sm font-medium text-gray-700">Contact</label>
            <Input {...register("contact")} placeholder="Contact" />
          </div>

          {/* Summary */}
          {/* <div className="space-y-2">
            <div className="flex justify-between">
              <span className="font-semibold">Sub Total</span>
              <span>0.00</span>
            </div>
            <div className="flex justify-between">
              <span className="font-semibold">Tax (18%)</span>
              <span>0.00</span>
            </div>
            <hr />
            <div className="flex justify-between">
              <span className="font-semibold">Total Amount</span>
              <span>0.00</span>
            </div>
          </div> */}

          <Button
            type="submit"
            className="w-full flex justify-center items-center gap-2"
          >
            <Save size={18} /> Generate Bill
          </Button>
        </Card>

        <AlertDialog
          open={showConfirmDialog}
          onOpenChange={setShowConfirmDialog}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Generate Bill</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to generate this bill?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                className={isLoading ? "cursor-not-allowed" : ""}
                disabled={isLoading}
                onClick={(e) => {
                  e.preventDefault();
                  handleConfirmSubmit();
                }}
              >
                Yes, Generate
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </form>
    </div>
  );
}
