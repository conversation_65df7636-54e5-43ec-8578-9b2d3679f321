import React, { useState, useEffect } from "react";
import { Typography } from "@/components/typography";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectLabel,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import {
  Eye,
  ChevronsUpDown,
  Check,
  RefreshCcw,
  ChevronRight,
  ChevronLeft,
} from "lucide-react";
import { FormProvider, useForm } from "react-hook-form";
// import { ComboBox } from "../shippingInstruction/ComboBox";
import { BLComboBox } from "./blComboBox";
import {
  fetchBillOfLadingList,
  filterConsignee,
  filterCarrier,
} from "@/services/admin/billOfLading";
import { useQuery } from "@tanstack/react-query";
import { useNavigate, useSearchParams } from "react-router-dom";
import Loader from "@/components/Loader";
import SpinnerLoader from "@/components/Loader/SpinnerLoader";
import { useFormContext } from "react-hook-form";
import { fetchBookingLocations } from "@/services/admin/booking";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { format, parse } from "date-fns";
import {
  formatDate,
  formatDateTime,
  formatDateTimeToGMT,
} from "@/utils/functions";
import dayjs from "dayjs";

const BillOfLaddingView = () => {
  const itemsPerPage = 10;

  const methods = useForm();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [currentPage, setCurrentPage] = useState(
    parseInt(searchParams.get("page") || "1", 10) || 1
  );
  const initialConsigneeQuery = searchParams.get("consignee") || "";
  const initialCarrierQuery = searchParams.get("carrier") || "";
  const initialPortOfLoad = {
    name: searchParams.get("pol_name") || "",
    location: searchParams.get("pol_location") || "",
    locode: searchParams.get("pol_locode") || "",
  };
  const [portOfLoadQuery, setPortOfLoadQuery] = useState("");
  const [portOfLoadOpen, setPortOfLoadOpen] = useState(false);
  const [shownValue, setShownValue] = useState(
    initialPortOfLoad.location || ""
  );

  const [consigneeQuery, setConsigneeQuery] = useState(initialConsigneeQuery);
  const [carrierQuery, setCarrierQuery] = useState(initialCarrierQuery);
  const [selectedFilter, setSelectedFilter] = useState<string>("");
  const portOfLoadValue = methods.watch("portOfLoad");
  const [filtersCleared, setFiltersCleared] = useState(false);

 useEffect(() => {
  const hasInitialValue =
    initialPortOfLoad.name || initialPortOfLoad.location || initialPortOfLoad.locode;

  if (hasInitialValue && !portOfLoadValue?.locode && !filtersCleared) {
    methods.setValue("portOfLoad", initialPortOfLoad);
  }
}, [initialPortOfLoad, portOfLoadValue, methods, filtersCleared]);

  const { data: billOfLadingListData, isLoading } = useQuery({
    queryKey: [
      "fetchBillOfLadingList",
      currentPage,
      // consigneeQuery,
      portOfLoadValue?.locode,
      carrierQuery,
    ],
    queryFn: () =>
      fetchBillOfLadingList(
        currentPage,
        // consigneeQuery,
        portOfLoadValue?.locode,
        carrierQuery
      ),
  });
  const totalCount = billOfLadingListData?.total_count || 0;
  const totalPages = Math.ceil(totalCount / itemsPerPage);

  // const { data: consigneeListData } = useQuery({
  //   queryKey: ["fetchConsigneeList"],
  //   queryFn: () => filterConsignee(),
  // });
  const consigneeListData = [];
  const { data: carrierListData } = useQuery({
    queryKey: ["fetchCarrierList"],
    queryFn: () => filterCarrier(),
  });

  const { data: portOfLoadData, isFetching: isPortOfLoadFetching } = useQuery({
    queryKey: ["fetchBookingLocation", { search: portOfLoadQuery }],
    queryFn: fetchBookingLocations,
  });

  const handleSearch = (val: string) => {
    console.log("Search value:", val);
    setPortOfLoadQuery(val);
  };

  useEffect(() => {
    fetchBillOfLadingList(
      currentPage,
      consigneeQuery,
      portOfLoadValue?.locode,
      carrierQuery
    );
  }, [currentPage, consigneeQuery, portOfLoadValue?.locode, carrierQuery]);

  const getFormattedDate = (dateString: string) => {
    if (!dateString) return "";
    const parsedDate = parse(dateString, "yyyy-MM-dd HH:mm:ss", new Date());
    const formatted = format(parsedDate, "MMM-dd-yyyy");
    return formatted;
  };
  const getstatusColorGradient = (status: string) => {
    switch (status) {
      case "New":
        return "#F1FFF8";
      case "Rejected":
        return "#FFEFEA";
      case "Acknowledged":
        return "#FFF5EA";
      case "Accepted":
        return "#E3EDFF";
      case "Open":
        return "#DFDFFF";
      case "Sent":
        return "#FFFBEB";
      default:
        return "#F3F4F6";
    }
  };
  const getstatusTextColor = (status: string) => {
    switch (status) {
      case "New":
        return "#339D59";
      case "Rejected":
        return "#DE7073";
      case "Acknowledged":
        return "#D0781E";
      case "Accepted":
        return "#4371C3";
      case "Open":
        return "#332076";
      case "Sent":
        return "#E1B60B";
      default:
        return "#6e7175ff";
    }
  };

  const getstatusBorderColor = (status: string) => {
    switch (status) {
      case "New":
        return "#339D59";
      case "Rejected":
        return "#DE7073";
      case "Acknowledged":
        return "#D0781E";
      case "Accepted":
        return "#4371C3";
      case "Open":
        return "#332076";
      case "Sent":
        return "#E1B60B";
      default:
        return "#8a8f97ff";
    }
  };
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen w-screen">
        <Loader />
      </div>
    );
  }
  return (
    <div>
      <div className="flex items-center justify-between">
        <p className="text-sm sm:text-base mt-5">
          {totalCount}{" "}
          <span className="text-gray-500">
            Result{totalCount > 1 ? "s" : ""} Found
          </span>
        </p>
        <div className="flex gap-4 items-center ml-auto">
          {/*  hided filter */}
          {/* <div className="relative flex mt-5">
            <Select
              value={selectedFilter}
              onValueChange={(value) => setSelectedFilter(value)}
            >
              <SelectTrigger className="min-w-[150px] h-11 border-[#D3DAE7]">
                <SelectValue placeholder="Filter By" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem key={"consignee"} value={"consignee"}>
                    Consignee
                  </SelectItem>
                  <SelectItem key={"port_of_load"} value={"port_of_load"}>
                    Port of Load
                  </SelectItem>
                  <SelectItem key={"carrier"} value={"carrier"}>
                    Carrier
                  </SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div> */}

          <div className="relative flex">
            <FormProvider {...methods}>
              <div className="flex gap-4 items-center ml-auto">
                {/* {selectedFilter === "port_of_load" && ( */}
                <FormField
                  control={methods.control}
                  name="portOfLoad"
                  render={({ field }) => {
                    const { value, onChange } = field;

                    return (
                      <FormItem className="w-[300px]">
                        <FormLabel className="flex justify-between items-center">
                          <span className="text-sm text-black font-medium">
                            Port of Load
                          </span>
                          {/* {(value?.name ||
                              value?.location ||
                              value?.locode) && (
                              <Typography
                                onClick={() => {
                                  methods.setValue("portOfLoad", {
                                    name: "",
                                    location: "",
                                    locode: "",
                                  });
                                  methods.setValue("portOfLoadBLas", "");
                                }}
                                variant="muted"
                                className="underline cursor-pointer"
                              >
                                Clear
                              </Typography>
                            )} */}
                        </FormLabel>
                        <FormControl>
                          <Popover
                            open={portOfLoadOpen}
                            onOpenChange={setPortOfLoadOpen}
                          >
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                className="justify-between w-full overflow-hidden h-11"
                              >
                                {value?.name ? (
                                  shownValue
                                ) : (
                                  <span className="text-gray-400">
                                    Select Location...
                                  </span>
                                )}
                                <ChevronsUpDown className="opacity-50" />
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-full p-0">
                              <Command>
                                <CommandInput
                                  placeholder="Search Location..."
                                  value={portOfLoadQuery}
                                  onValueChange={(value) => {
                                    handleSearch(value);
                                  }}
                                  className="h-9"
                                />
                                <CommandList>
                                  <CommandEmpty>
                                    {isPortOfLoadFetching ? (
                                      <div className="flex justify-center w-full">
                                        <SpinnerLoader />
                                      </div>
                                    ) : (
                                      "No Location Found."
                                    )}
                                  </CommandEmpty>
                                  <CommandGroup>
                                    {portOfLoadData?.message?.results?.map(
                                      (location) => {
                                        const locationStr = `${location.country} (${
                                          location.locode
                                        })`;

                                        return (
                                          <CommandItem
                                            key={location.name}
                                            value={`${
                                              location?.location_name
                                            }, ${
                                              location?.sub_division
                                                ? `${location?.sub_division},`
                                                : ""
                                            } ${location?.country} (${
                                              location?.locode
                                            })`}
                                            onSelect={() => {
                                              onChange({
                                                name: String(location.name),
                                                location: locationStr,
                                                locode: location.locode,
                                              });

                                              methods.setValue(
                                                "portOfLoadBLas",
                                                `${location.location_name}, ${
                                                  location.sub_division
                                                    ? `${location.sub_division}, `
                                                    : ""
                                                }${location.country}`
                                              );

                                              setShownValue(locationStr);
                                              setPortOfLoadOpen(false);

                                              setSearchParams((prev) => {
                                                const newParams =
                                                  new URLSearchParams(prev);
                                                newParams.set(
                                                  "pol_name",
                                                  location.name
                                                );
                                                newParams.set(
                                                  "pol_location",
                                                  locationStr
                                                );
                                                newParams.set(
                                                  "pol_locode",
                                                  location.locode
                                                );
                                                return newParams;
                                              });
                                            }}
                                          >
                                            {locationStr}
                                            <Check
                                              className={`ml-auto ${
                                                value?.name === location.name
                                                  ? "opacity-100"
                                                  : "opacity-0"
                                              }`}
                                            />
                                          </CommandItem>
                                        );
                                      }
                                    )}
                                  </CommandGroup>
                                </CommandList>
                              </Command>
                            </PopoverContent>
                          </Popover>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
                {/* // )} */}
              </div>
            </FormProvider>
          </div>

          {/* {selectedFilter === "consignee" && ( */}
          {/* <BLComboBox
            label="Consignee"
            required={false}
            value={consigneeQuery}
            onChange={(value) => {
              setConsigneeQuery(value);
              setCurrentPage(1);
            }}
            options={consigneeListData?.message?.cosignee.map((c: any) => ({
              label: c.customer_name,
              value: c.name,
            }))}
            placeholder="Select Consignee"
          /> */}
          {/* // )} */}
          {/* {selectedFilter === "carrier" && ( */}
          <BLComboBox
            label="Carriers"
            required={false}
            value={carrierQuery}
            onChange={(value) => {
              setCarrierQuery(value);
              setCurrentPage(1);

              setSearchParams((prev) => {
                const newParams = new URLSearchParams(prev);
                newParams.set("carrier", value);
                return newParams;
              });
            }}
            options={carrierListData?.message?.carriers.map((c: any) => ({
              label: c.partyname1,
              value: c.partyalias,
            }))}
            placeholder="Select Carrier"
          />
          {/* )} */}
          {(portOfLoadQuery ||
            carrierQuery ||
            consigneeQuery ||
            (portOfLoadValue && portOfLoadValue.locode !== "")) && (
            <div className="relative flex mt-6">
              <Button
                variant="outline"
                className="h-11"
                onClick={() => {
                  setFiltersCleared(true);
                  setPortOfLoadQuery("");
                  setCarrierQuery("");
                  setConsigneeQuery("");
                  setCurrentPage(1);

                  // Clear the portOfLoad field in React Hook Form
                  // Clear React Hook Form state
                  methods.reset({
                    portOfLoad: {
                      name: "",
                      location: "",
                      locode: "",
                    },
                  });
                  // Optionally reset the shown display value
                  setShownValue("");
                  setSearchParams({});
                }}
              >
                <RefreshCcw /> Clear Filters
              </Button>
            </div>
          )}
        </div>
      </div>
      <div className="mt-10 overflow-y-auto ">
        <ScrollArea className="whitespace-nowrap">
          <Table className="table-auto border-1 border-[#D3DAE7] w-full">
            <TableHeader>
              <TableRow className="bg-[#E5E8EF]">
                {/* <TableHead className="pl-6 w-[10%]">
                                    <Checkbox className='bg-white' />
                                </TableHead> */}
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  <div className="flex flex-col">
                    <span>Bl Number</span>
                    <span className="text-xs text-gray-400">
                      Carrier booking number
                    </span>
                  </div>
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  <div className="flex flex-col">
                    <span>POL</span>
                    <span className="text-xs text-gray-400">Sail Date</span>
                  </div>
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  POD
                </TableHead>
                {/* <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  Carrier Booking Number
                </TableHead> */}
                <TableHead className="p-3 w-[5%] font-bold text-[#191C36]">
                  Carrier
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  Received Date
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  Docket Status
                </TableHead>
                <TableHead className="p-3 w-[5%] font-bold text-[#191C36]">
                  BOL Status
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  ACTION
                </TableHead>
                {/* <TableHead className="p-3 w-[10%]">
                                    <ComboBox headTitle="Forwarder Reference(s)" />
                                </TableHead>
                                <TableHead className="p-3 w-[10%]">
                                    <ComboBox headTitle="Carrier Booking Number(s)" />
                                </TableHead> */}
              </TableRow>
            </TableHeader>
            <TableBody>
              {billOfLadingListData?.message &&
              billOfLadingListData.message.length > 0 ? (
                billOfLadingListData.message.map((item: any, index: number) => (
                  <TableRow key={index} className="h-0">
                    <TableCell className="p-3">
                      <div className="flex flex-col">
                        {item?.bol_number}
                        <span className="text-xs text-gray-500">
                          {item?.carrier_booking_number}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="p-3">
                      <div className="flex flex-col">
                        {item?.port_of_load_location}, ({item?.port_of_load})
                        <span className="text-xs text-gray-500">
                          {getFormattedDate(item?.main_transport_sail_date)}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="p-3">
                      {item?.port_of_discharge_location}, (
                      {item?.port_of_discharge})
                    </TableCell>
                    {/* <TableCell className="p-3">
                      {item?.carrier_booking_number}
                    </TableCell> */}
                    <TableCell className="p-3">{item?.carrier}</TableCell>
                    <TableCell className="p-3">
                      {item?.create_date_time ? dayjs(item?.create_date_time).format("MMM-DD-YYYY hh:mm A") : ""}
                    </TableCell>
                    <TableCell className="p-3">
                      <span
                        className={`px-3 py-1 rounded-sm text-sm font-normal`}
                        style={{
                          color: getstatusTextColor(item.docket_status),
                          borderColor: getstatusBorderColor(item.docket_status),
                          backgroundColor: getstatusColorGradient(
                            item.docket_status
                          ),
                          borderWidth: "1px",
                          borderStyle: "solid",
                        }}
                      >
                        {item?.docket_status ?? "To Be Created"}
                      </span>
                    </TableCell>
                    <TableCell className="p-3">
                      <span className="font-bold text-gray-500">
                        {item?.message_status}
                      </span>
                    </TableCell>

                    <TableCell className="p-3 flex items-center gap-2">
                      <Button
                        variant={"outline"}
                        className="border-2 border-primary bg-primary hover:bg-orange-600 text-white rounded-sm"
                        onClick={() =>
                          navigate(
                            `/dashboard/documentation/my-bill-of-ladding/${item.name}`
                          )
                        }
                      >
                        <Eye />
                      </Button>
                      <Button
                        type="button"
                        variant={"outline"}
                        className={`border-2 rounded-sm ${
                          item?.docket_id
                            ? "bg-gray-300 text-gray-600 border-gray-300 "
                            : "bg-primary text-white border-primary hover:bg-orange-600"
                        }`}
                        onClick={() => {
                          if (item?.docket_id) {
                            navigate(
                              `/dashboard/customers/customer-docket-view/${item?.docket_id}?carrierBooking=${item?.carrier_booking_number}`
                            );
                          } else {
                            navigate(
                              `/dashboard/customers/create-docket/${item?.carrier_booking_number}`
                            );
                          }
                        }}
                      >
                        {item?.docket_id ? "View Docket" : "Create Docket"}
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={5}
                    className="text-center text-[#929FB8] py-4"
                  >
                    No rows to show
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>
      <div className="flex items-center justify-between mt-6">
        <div className="flex items-center gap-2 mx-auto sm:mx-0 sm:ml-auto">
          <Button
            className="rounded-lg px-3 py-2"
            variant="outline"
            onClick={() => {
              setCurrentPage((prev) => Math.max(prev - 1, 1));
              setSearchParams((prev) => {
                const newParams = new URLSearchParams(prev);
                newParams.set("page", String(currentPage - 1));
                return newParams;
              });
            }}
            disabled={currentPage === 1}
            size="sm"
          >
            <ChevronLeft className="text-black h-4 w-4" />
          </Button>
          <div className="flex gap-1">
            {Array.from({ length: Math.min(totalPages, 5) }).map((_, index) => {
              let page;
              if (totalPages <= 5) {
                page = index + 1;
              } else if (currentPage <= 3) {
                page = index + 1;
              } else if (currentPage >= totalPages - 2) {
                page = totalPages - 4 + index;
              } else {
                page = currentPage - 2 + index;
              }

              return (
                <Button
                  key={page}
                  onClick={() => {
                    setCurrentPage(page);
                    setSearchParams((prev) => {
                      const newParams = new URLSearchParams(prev);
                      newParams.set("page", String(page));
                      return newParams;
                    });
                  }}
                  className={`rounded-lg px-3 py-2 ${
                    page === currentPage
                      ? "bg-white text-black border"
                      : "text-gray-500"
                  }`}
                  variant="outline"
                  size="sm"
                >
                  {page}
                </Button>
              );
            })}
            {totalPages > 5 && currentPage < totalPages - 2 && (
              <>
                <span className="flex items-center px-2">...</span>
                <Button
                  onClick={() => {
                    setCurrentPage(totalPages);
                    setSearchParams((prev) => {
                      const newParams = new URLSearchParams(prev);
                      newParams.set("page", String(totalPages));
                      return newParams;
                    });
                  }}
                  className={`rounded-lg px-3 py-2 ${
                    totalPages === currentPage
                      ? "bg-white text-black border"
                      : "text-gray-500"
                  }`}
                  variant="outline"
                  size="sm"
                >
                  {totalPages}
                </Button>
              </>
            )}
          </div>
          <Button
            className="rounded-lg px-3 py-2"
            variant="outline"
            onClick={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            disabled={currentPage === totalPages}
            size="sm"
          >
            <ChevronRight className="text-black h-4 w-4" />
          </Button>
        </div>
      </div>
      {/* <div className="mt-8 flex justify-between items-center pr-8">
                <Typography className="text-[#929FB8]">
                    Showing 1 to 1 of 1 entries
                </Typography>
                <div className="flex gap-3">
                    <Button variant={"outline"}>
                        <ChevronLeft className="text-[#929FB8]" />
                        <span className="text-[#929FB8]">Previous</span>
                    </Button>
                    <Button variant={"outline"}>
                        <span className="text-[#929FB8]">Next</span>
                        <ChevronRight className="text-[#929FB8]" />
                    </Button>
                </div>
            </div> */}
    </div>
  );
};

export default BillOfLaddingView;
