import { useState, useEffect, useRef } from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import {
  Eye,
  Printer,
  Download,
  Search,
  ChevronLeft,
  ChevronRight,
  RefreshCcw,
  Pencil,
  Plus,
  Trash,
  FileSearch2,
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import {
  listInvoice,
  deleteInvoiceById,
} from "@/services/admin/invoiceGenerate";
import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { useSearchParams } from "react-router-dom";
import dayjs from "dayjs";
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";

import { toast } from "sonner";
import { useMutation, useQueryClient } from "@tanstack/react-query";

type InvoiceStatus =
  | "Paid"
  | "Unpaid"
  | "Open"
  | "Overdue"
  | "Viewed"
  | "Pending Payment";

// const invoices = [
//   {
//     customer: {
//       name: "Thai Nippon Foods",
//       address: "Samut Prakan, Thailand",
//       avatar: "https://i.pravatar.cc/40?img=1",
//     },
//     invoiceNo: "INV-20240417-01",
//     port: "Laem Chabang",
//     issueDate: "17 Apr 2024",
//     dueDate: "30 Apr 2024",
//     amount: "฿120,000.00",
//     status: "Paid",
//   },
//   {
//     customer: {
//       name: "Yamato Co. Ltd.",
//       address: "Bangkok, Thailand",
//       avatar: "https://i.pravatar.cc/40?img=2",
//     },
//     invoiceNo: "INV-20240417-02",
//     port: "Bangkok Port",
//     issueDate: "18 Apr 2024",
//     dueDate: "2 May 2024",
//     amount: "฿80,000.00",
//     status: "Overdue",
//   },
// ];

const statusColor: Record<InvoiceStatus, string> = {
  Paid: "bg-green-50 text-green-600 border border-green-200",
  Unpaid: "bg-orange-50 text-orange-600 border border-orange-200",
  Open: "bg-violet-50 text-violet-600 border border-violet-200",
  Overdue: "bg-red-50 text-red-600 border border-red-200",
  Viewed: "bg-blue-50 text-blue-600 border border-blue-200",
  "Pending Payment": "bg-yellow-50 text-yellow-600 border border-yellow-200",
};
function getStatusColor(status: string): string {
  if (status in statusColor) {
    return statusColor[status as InvoiceStatus]; // Safe cast after check
  }
  return "bg-violet-50 text-violet-600 border border-violet-200"; // fallback style
}

export default function InvoiceList() {
  const [searchParams, setSearchParams] = useSearchParams();
  const initialStatusFilter = searchParams.get("status") || "";
  const initialDateFilter = searchParams.get("date") || "";
  const initialSearch = searchParams.get("search") || "";
  const [statusFilter, setStatusFilter] = useState(initialStatusFilter);
  const [dateFilter, setDateFilter] = useState(initialDateFilter);
  const [search, setSearch] = useState(initialSearch);
  const [currentPage, setCurrentPage] = useState(
    parseInt(searchParams.get("page") || "1", 10) || 1
  );

  const rowsPerPage = 10;
  const [selectedInvoices, setSelectedInvoices] = useState<number[]>([]);
  const navigate = useNavigate();
  const [openDialog, setOpenDialog] = useState(false);
  const [deleteId, setDeleteId] = useState<string | null>(null);

  const toggleCheckbox = (index: number) => {
    setSelectedInvoices((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
    );
  };

  // useEffect(() => {
  //   fetchInvoiceList(statusFilter, dateFilter, search);
  // }, [statusFilter, dateFilter, search]);

  const {
    data: fetchInvoiceList,
    isLoading,
    error,
    refetch: refetchInvoiceList,
  } = useQuery({
    queryKey: ["listInvoice", statusFilter, dateFilter, search],
    queryFn: () =>
      listInvoice({
        ...(statusFilter && { status_filter: statusFilter }),
        ...(dateFilter && { date: dateFilter }),
        ...(search && { search }),
      }),
  });
  const totalCount = fetchInvoiceList?.message?.invoices?.length || 0;
  const totalPages = Math.ceil(totalCount / rowsPerPage);

  const paginatedData =
    fetchInvoiceList?.message?.invoices?.slice(
      (currentPage - 1) * rowsPerPage,
      currentPage * rowsPerPage
    ) || [];
  const selectAllRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (selectAllRef.current) {
      const isIndeterminate =
        selectedInvoices.length > 0 &&
        selectedInvoices.length < fetchInvoiceList.length;
      selectAllRef.current.indeterminate = isIndeterminate;
    }
  }, [selectedInvoices]);
  const isSelected = (index: number) => selectedInvoices.includes(index);

  console.log(fetchInvoiceList);
  const nullId = "";

  const useDeleteInvoice = () => {
    const queryClient = useQueryClient();

    return useMutation({
      mutationFn: (invoice_name: string) => deleteInvoiceById(invoice_name),
      onSuccess: (response) => {
        if (response?.message?.status_code === 200) {
          toast.success(
            response.message?.message || "Invoice deleted successfully"
          );
          refetchInvoiceList();
          setOpenDialog(false);
          setDeleteId(null);
          queryClient.invalidateQueries({ queryKey: ["invoices"] });
        } else {
          toast.error(response.message?.message || "Failed to delete invoice");
          setOpenDialog(false);
          setDeleteId(null);
        }
      },
      onError: (err: any) => {
        toast.error(err?.message || "Failed to delete invoice");
      },
    });
  };
  const { mutate: deleteInvoice, isPending: isDeleting } = useDeleteInvoice();

  return (
    <Card
      className="w-full shadow-none border-none"
      style={{ backgroundColor: "#f9fafc00" }}
    >
      <CardContent className="p-0">
        <div className="flex flex-wrap justify-between items-center gap-3 p-0 pb-3 ">
          {/* Status Tabs */}
          <div className="flex flex-wrap items-center gap-4">
            <Button variant="outline">
              All{" "}
              <span className="ml-1 text-gray-500">
                {fetchInvoiceList?.message?.counts?.total || 0}
              </span>
            </Button>
            <Button variant="ghost" className="">
              Open Invoice{" "}
              <span className="ml-1 text-[#5B77FF]">
                {fetchInvoiceList?.message?.counts?.open_invoice || 0}
              </span>
            </Button>
            <Button variant="ghost" className="">
              Paid{" "}
              <span className="ml-1 text-[#42C37B]">
                {fetchInvoiceList?.message?.counts?.paid || 0}
              </span>
            </Button>
            <Button variant="ghost" className="">
              Overdue{" "}
              <span className="ml-1 text-[#FF6B6B]">
                {fetchInvoiceList?.message?.counts?.overdue || 0}
              </span>
            </Button>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap items-center gap-3">
            {/* Date Filter */}
            <input
              type="date"
              className="border border-gray-300 rounded-md px-3 text-sm h-[40px]"
              value={dateFilter}
              onChange={(e) => {
                setDateFilter(e.target.value);
                setSearchParams((prev) => {
                  const newParams = new URLSearchParams(prev);
                  newParams.set("date", e.target.value);
                  newParams.set("page", "1");
                  return newParams;
                });
              }}
            />

            {/* Search Input */}
            <div className="relative">
              <Input
                className="pr-10 text-sm h-[40px]"
                placeholder="Search here"
                value={search}
                onChange={(e) => {
                  setSearch(e.target.value);
                  setSearchParams((prev) => {
                    const newParams = new URLSearchParams(prev);
                    newParams.set("search", e.target.value);
                    newParams.set("page", "1");
                    return newParams;
                  });
                }}
              />
              <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            </div>

            {/* Status Dropdown */}
            <select
              className="border border-gray-300 rounded-md px-2 text-sm h-[40px]"
              value={statusFilter}
              onChange={(e) => {
                setStatusFilter(e.target.value);
                setSearchParams((prev) => {
                  const newParams = new URLSearchParams(prev);
                  newParams.set("status", e.target.value);
                  newParams.set("page", "1");
                  return newParams;
                });
              }}
            >
              <option value="">Status</option>
              <option value="Viewed">Viewed</option>
              <option value="Open">Open Invoice</option>
              <option value="Paid">Paid</option>
              <option value="Overdue">Overdue</option>
            </select>
            <div className="relative flex">
              {(statusFilter || search || dateFilter) && (
                <Button
                  variant="outline"
                  className="h-11"
                  onClick={() => {
                    setStatusFilter("");
                    setDateFilter("");
                    setSearch("");
                    setSearchParams({});
                    setCurrentPage(1);
                  }}
                >
                  <RefreshCcw /> Clear Filters
                </Button>
              )}
            </div>
            <Button
              onClick={() =>
                navigate(
                  `/dashboard/customers/generate-invoice?docketId=${nullId}`
                )
              }
              className="bg-foreground h-10 px-4 text-white text-sm"
            >
              <Plus className="mr-2 h-4 w-4" />
              Create Invoice
            </Button>
          </div>
        </div>
        {/* <p className="text-sm sm:text-base mt-5">
          {totalCount}{" "}
          <span className="text-gray-500">
            Result{totalCount > 1 ? "s" : ""} Found
          </span>
        </p> */}
        <ScrollArea className="w-full">
          <Table className="table-auto w-full border-separate border-spacing-y-2 text-sm">
            <TableHeader>
              <TableRow className="bg-[#D3DAE7] h-[55px]">
                {/* <TableHead className="p-3 w-[3%]">

                  <input
                    type="checkbox"
                    onChange={(e) =>
                      setSelectedInvoices(
                        e.target.checked ? invoices.map((_, idx) => idx) : []
                      )
                    }
                    checked={selectedInvoices.length === invoices.length}
                  />
                </TableHead> */}

                <TableHead className="p-3 pl-6 font-bold text-[#191C36] w-[12%]">
                  INVOICE
                </TableHead>
                <TableHead className="p-3 pl-6 font-bold text-[#191C36] w-[20%]">
                  CUSTOMER
                </TableHead>
                <TableHead className="p-3 pl-6 font-bold text-[#191C36] w-[12%]">
                  ORIGIN PORT
                </TableHead>
                <TableHead className="p-3 pl-6 font-bold text-[#191C36] w-[10%]">
                  ISSUE DATE
                </TableHead>
                <TableHead className="p-3 pl-6 font-bold text-[#191C36] w-[10%]">
                  DUE DATE
                </TableHead>
                <TableHead className="p-3 pl-6 font-bold text-[#191C36] w-[12%]">
                  AMOUNT
                </TableHead>
                <TableHead className="p-3 pl-6 font-bold text-[#191C36] w-[10%]">
                  STATUS
                </TableHead>
                <TableHead className="p-3 pl-6 font-bold text-[#191C36] w-[14%] text-center">
                  ACTION
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedData?.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={10} className="text-center py-8">
                    <div className="flex flex-col items-center justify-center gap-2">
                      <FileSearch2 className="w-10 h-10 text-gray-400" />
                      <span className="text-gray-500 font-medium">
                        No data found
                      </span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                paginatedData?.map((inv: any, idx: any) => (
                  <TableRow
                    key={idx}
                    className={`border border-[#D3DAE7] hover:bg-[#D3DAE7] transition duration-200 shadow-sm ${
                      isSelected(idx) ? "bg-blue-50" : ""
                    }`}
                  >
                    {/* <TableCell className="p-3 text-left">
                    <input
                      type="checkbox"
                      checked={isSelected(idx)}
                      onChange={() => toggleCheckbox(idx)}
                    />
                  </TableCell> */}

                    <TableCell className="p-3 pl-6">
                      {inv.invoice_number}
                    </TableCell>
                    <TableCell className="p-3 pl-6">
                      <div className="flex items-center gap-3">
                        {/* Avatar hidden for now */}
                        <div className="flex flex-col">
                          <span className="font-medium">
                            {inv.quickbooks_customer_name}
                          </span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="p-3 pl-6">{inv.orgin_port}</TableCell>
                    <TableCell className="p-3 pl-6">
                      {inv.invoice_date
                        ? dayjs(inv.invoice_date).format("MMM-DD-YYYY")
                        : ""}
                    </TableCell>
                    <TableCell className="p-3 pl-6">
                      {inv.due_date
                        ? dayjs(inv.due_date).format("MMM-DD-YYYY")
                        : ""}
                    </TableCell>
                    <TableCell className="p-3 pl-6">
                      {inv.total_amount}
                    </TableCell>
                    <TableCell className="p-3 pl-6">
                      <span
                        className={`px-3 py-1 text-xs font-normal rounded-md ${getStatusColor(
                          inv.status
                        )}`}
                      >
                        {inv.status ?? "Open"}
                      </span>
                    </TableCell>
                    <TableCell className="p-3 pl-6 text-center">
                      <div className="flex justify-center gap-2">
                        <Button
                          size="icon"
                          variant={"outline"}
                          className="border-2 border-grey bg-grey hover:bg-orange-600 text-black rounded-sm px-3 py-2"
                          onClick={() =>
                            (window.location.href = `/dashboard/customers/update-invoice/${inv.name}?docketId=${inv?.docket_id}`)
                          }
                          disabled={inv.status === "Paid" ? true : false}
                        >
                          <Pencil />
                        </Button>

                        <Button
                          size="icon"
                          variant={"outline"}
                          className="border-2 border-primary bg-primary hover:bg-orange-600 text-white rounded-sm px-3 py-2"
                          onClick={() =>
                            navigate(
                              `/dashboard/customers/view-invoice/${inv.name}`
                            )
                          }
                        >
                          <Eye />
                        </Button>
                        <Button
                          variant={"outline"}
                          className={`border-2 border-grey bg-grey hover:bg-orange-600 text-black rounded-sm px-3 py-2 ${
                            isDeleting ? "cursor-not-allowed" : ""
                          }`}
                          disabled={isDeleting}
                          onClick={() => {
                            setDeleteId(inv.name);
                            setOpenDialog(true);
                          }}
                        >
                          <Trash />
                        </Button>

                        {/* <Button size="icon" variant="ghost">
                        <Printer className="h-4 w-4" />
                      </Button>
                      <Button size="icon" variant="ghost">
                        <Download className="h-4 w-4" />
                      </Button> */}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>

        <AlertDialog open={openDialog} onOpenChange={setOpenDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Invoice</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete this Invoice?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                className={isDeleting ? "cursor-not-allowed" : ""}
                disabled={isDeleting}
                onClick={(e) => {
                  e.preventDefault();
                  if (deleteId) {
                    deleteInvoice(deleteId);
                  }
                }}
              >
                Yes, Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardContent>

      <div className="flex items-center justify-between mt-6">
        <div className="flex items-center gap-2 mx-auto sm:mx-0 sm:ml-auto">
          <Button
            className="rounded-lg px-3 py-2"
            variant="outline"
            onClick={() => {
              setCurrentPage((prev) => Math.max(prev - 1, 1));
              setSearchParams((prev) => {
                const newParams = new URLSearchParams(prev);
                newParams.set("page", String(currentPage - 1));
                return newParams;
              });
            }}
            disabled={currentPage === 1}
            size="sm"
          >
            <ChevronLeft className="text-black h-4 w-4" />
          </Button>
          <div className="flex gap-1">
            {Array.from({ length: Math.min(totalPages, 5) }).map((_, index) => {
              let page;
              if (totalPages <= 5) {
                page = index + 1;
              } else if (currentPage <= 3) {
                page = index + 1;
              } else if (currentPage >= totalPages - 2) {
                page = totalPages - 4 + index;
              } else {
                page = currentPage - 2 + index;
              }

              return (
                <Button
                  key={page}
                  onClick={() => {
                    setCurrentPage(page);
                    setSearchParams((prev) => {
                      const newParams = new URLSearchParams(prev);
                      newParams.set("page", String(page));
                      return newParams;
                    });
                  }}
                  className={`rounded-lg px-3 py-2 ${
                    page === currentPage
                      ? "bg-white text-black border"
                      : "text-gray-500"
                  }`}
                  variant="outline"
                  size="sm"
                >
                  {page}
                </Button>
              );
            })}
            {totalPages > 5 && currentPage < totalPages - 2 && (
              <>
                <span className="flex items-center px-2">...</span>
                <Button
                  onClick={() => {
                    setCurrentPage(totalPages);
                    setSearchParams((prev) => {
                      const newParams = new URLSearchParams(prev);
                      newParams.set("page", String(totalPages));
                      return newParams;
                    });
                  }}
                  className={`rounded-lg px-3 py-2 ${
                    totalPages === currentPage
                      ? "bg-white text-black border"
                      : "text-gray-500"
                  }`}
                  variant="outline"
                  size="sm"
                >
                  {totalPages}
                </Button>
              </>
            )}
          </div>
          <Button
            className="rounded-lg px-3 py-2"
            variant="outline"
            onClick={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            disabled={currentPage === totalPages}
            size="sm"
          >
            <ChevronRight className="text-black h-4 w-4" />
          </Button>
        </div>
      </div>
    </Card>
  );
}
