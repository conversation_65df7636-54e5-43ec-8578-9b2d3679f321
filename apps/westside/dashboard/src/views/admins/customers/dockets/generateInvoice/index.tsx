import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Trash2, Minus } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card } from "@/components/ui/card";
import { useFieldArray, useForm } from "react-hook-form";
import { Plus, PlusCircle, Image, Save } from "lucide-react";
import { cn } from "@/lib/utils"; // If using class merging
import { fetchBookingLocations } from "@/services/admin/booking";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Controller } from "react-hook-form";
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { FormProvider } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import SpinnerLoader from "@/components/Loader/SpinnerLoader";
import { Eye, ChevronsUpDown, Check, X } from "lucide-react";
import { useParams } from "react-router-dom";
import {
  getQbDocketDetails,
  getQuickbooksCustomers,
  getQuickbooksItems,
  createQuickbooksInvoice,
  getAllCustomers,
} from "@/services/admin/invoiceGenerate";
import { ComboBoxPair } from "@/components/ui/comboBox-pair";
import { toast } from "sonner";
import { Navigate, useNavigate, useSearchParams } from "react-router";

export default function GenerateInvoicePage() {
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const params = new URLSearchParams(searchParams);
  const id = params.get("docketId");
  const {
    register,
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm({
    defaultValues: {
      customer_name: "",
      invoiceDate: "",
      bookingNo: "",
      hsCode: "",
      orgin_port: "",
      destination_port: "",
      bolNo: "",
      shippingDate: "",
      dueDate: "",
      incoterm: "",
      comments: "",
      attachment: undefined,
      quickbooks_customer_id: "",
      address: "",
      email_address: "",
      contact_number: "",
      items: [
        {
          product_services: "",
          product_description: "",
          qty: 1,
          uom: "",
          unit_price: 0,
          amount: 0,
          item_ref_id: "",
        },
      ],
    },
  });
  const methods = useForm();
  const { fields, append, remove } = useFieldArray({
    control,
    name: "items",
  });

  const [attachments, setAttachments] = useState<File[]>([
    undefined as unknown as File,
  ]);
  const [originPortQuery, setOriginPortQuery] = useState("");
  const [originPortOpen, setOriginPortOpen] = useState(false);
  const [shownOriginValue, setShownOriginValue] = useState("");

  // Destination Port state
  const [destinationPortQuery, setDestinationPortQuery] = useState("");
  const [destinationPortOpen, setPortOfDestinationOpen] = useState(false);
  const [shownDestinationValue, setShownDestinationValue] = useState("");
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [formPayload, setFormPayload] = useState<any | null>(null);

  const { data: originPortData, isFetching: isOriginFetching } = useQuery({
    queryKey: ["fetchOriginPortLocation", { search: originPortQuery }],
    queryFn: fetchBookingLocations,
  });
  // Destination Port API
  const { data: destinationPortData, isFetching: isDestinationFetching } =
    useQuery({
      queryKey: [
        "fetchDestinationPortLocation",
        { search: destinationPortQuery },
      ],
      queryFn: fetchBookingLocations,
    });

  const { data: QbDocketDetailsData } = useQuery({
    queryKey: ["getQbDocketDetails", { docketId: id }],
    queryFn: () => getQbDocketDetails(id as string),
    enabled: !!id,
  });

  const { data: quickbooksCustomersData } = useQuery({
    queryKey: ["getQuickbooksCustomers"],
    queryFn: getQuickbooksCustomers,
  });
  const [productSearch, setProductSearch] = useState("");
  const { data: quickbooksItemsData, isLoading: quickbooksItemsDataIsLoading } =
    useQuery({
      queryKey: ["getQuickbooksItems", productSearch],
      queryFn: () => getQuickbooksItems(productSearch),
      enabled: !!productSearch,
    });
  const { data: customerDetails } = useQuery({
    queryKey: ["getAllCustomers"],
    queryFn: () => getAllCustomers(),
  });

  const handleAttachmentChange = (e, index) => {
    const file = e.target.files?.[0];
    if (!file) return;
    setAttachments((prev) => {
      const updated = [...prev];
      updated[index] = file;
      return updated;
    });
  };

  const handleAddAttachment = () => {
    setAttachments((prev) => [...prev, undefined as unknown as File]);
  };

  const handleRemoveAttachment = (index: number) => {
    setAttachments((prev) => prev.filter((_, i) => i !== index));
  };
  const formatDateForInput = (dateString: string) => {
    const date = new Date(dateString);
    return !isNaN(date.getTime()) ? date.toISOString().split("T")[0] : "";
  };
  const customerOptions =
    quickbooksCustomersData?.message?.customers?.map((customer: any) => ({
      label: customer.display_name,
      value: customer.id,
    })) || [];

  const itemOptions =
    quickbooksItemsData?.message?.items?.map((item: any) => ({
      label: item.name,
      value: item.name,
    })) || [];

  const docketCustomerOptions =
    customerDetails?.message?.data?.map((customer: any) => ({
      label: customer.customer_name,
      value: customer.id,
    })) || [];

  useEffect(() => {
    if (QbDocketDetailsData?.message?.data?.hs_code) {
      setValue("hsCode", QbDocketDetailsData?.message?.data?.hs_code);
    }
    if (QbDocketDetailsData?.message?.data?.carrier_booking_number) {
      setValue(
        "bookingNo",
        QbDocketDetailsData?.message?.data?.carrier_booking_number
      );
    }
    if (QbDocketDetailsData?.message?.data?.terms) {
      setValue("incoterm", QbDocketDetailsData?.message?.data?.terms);
    }
    if (QbDocketDetailsData?.message?.data?.shipping_date) {
      setValue(
        "shippingDate",
        formatDateForInput(QbDocketDetailsData?.message?.data?.shipping_date)
      );
    }

    if (QbDocketDetailsData?.message?.data?.destination) {
      setValue(
        "destination_port",
        QbDocketDetailsData?.message?.data?.destination
      );
    }
    if (QbDocketDetailsData?.message?.data?.origin) {
      setValue("orgin_port", QbDocketDetailsData?.message?.data?.origin);
    }

    if (QbDocketDetailsData?.message?.data?.blno) {
      setValue("bolNo", QbDocketDetailsData?.message?.data?.blno);
    }
    if (QbDocketDetailsData?.message?.data?.customer_id) {
      setValue(
        "customer_name",
        QbDocketDetailsData?.message?.data?.customer_id
      );
    }
  }, [QbDocketDetailsData, setValue]);
  const docketCustomer = watch("customer_name");
  const [selectedDocketCustomerLabel, setSelectedDocketCustomerLabel] =
    useState("");
  useEffect(() => {
    if (!docketCustomer) return;

    const selectedOption = docketCustomerOptions.find(
      (opt) => opt.value === docketCustomer
    );

    if (selectedOption) {
      setSelectedDocketCustomerLabel(selectedOption.label);
    }
  }, [docketCustomer, docketCustomerOptions]);

  const selectedCustomerId = watch("quickbooks_customer_id");
  const [quickbookCustomerLabel, setQuickbookCustomerLabel] = useState("");
  useEffect(() => {
    if (!selectedCustomerId) return;

    const selectedCustomer = quickbooksCustomersData?.message?.customers?.find(
      (customer: any) => customer.id === selectedCustomerId
    );

    if (selectedCustomer?.email) {
      setValue("email_address", selectedCustomer.email);
    } else {
      setValue("email_address", " ");
    }
    if (selectedCustomer?.billing_address) {
      const addr = selectedCustomer.billing_address;
      const fullAddress = `${addr.Line1 || ""}, ${addr.City || ""}, ${
        addr.CountrySubDivisionCode || ""
      } ${addr.PostalCode || ""}`;
      setValue("address", fullAddress.trim());
    } else {
      setValue("address", " ");
    }

    if (selectedCustomer?.phone) {
      setValue("contact_number", selectedCustomer.phone);
    } else {
      setValue("contact_number", " ");
    }

    if (selectedCustomer?.display_name) {
      setQuickbookCustomerLabel(selectedCustomer.display_name);
    }
  }, [selectedCustomerId, quickbooksCustomersData, setValue]);

  const onSubmit = (data: any) => {
    setFormPayload(data);
    setShowConfirmDialog(true); // Show confirmation dialog
  };

  const handleConfirmSubmit = async () => {
    setIsLoading(true);
    if (!formPayload) return;
    console.log(formPayload);
    const data = formPayload;

    let itemsDetails: any[] = [];

    const isOnlyEmptyItem =
      data.items.length === 1 && !data.items[0].item_ref_id;

    if (isOnlyEmptyItem) {
      toast.error("Please add at least one item to the invoice.");
      setShowConfirmDialog(false);
      setIsLoading(false);
      itemsDetails = [{}]; // Or simply `[]` if you want to skip empty items
      return false;
    } else {
      itemsDetails = data.items
        .filter(
          (item: any) =>
            item.product_services && item.product_services.trim() !== ""
        )
        .map((item: any) => {
          const amount = Number(item.qty) * Number(item.unit_price);
          return {
            product_services: item.product_services,
            product_description: item.product_description,
            quantity: Number(item.qty),
            rate: Number(item.unit_price),
            amount,
            item_ref_id: item.item_ref_id,
            uom: item.uom || "",
          };
        });
    }

    const payload = {
      customer_id: data.customer_name, // You can remove this if not needed
      quickbooks_customer_id: data.quickbooks_customer_id,
      invoice_data: {
        docket_id: id || "", // Docket ID if available
        customer_name: selectedDocketCustomerLabel,
        quickbooks_customer_name: quickbookCustomerLabel,
        email_address: data.email_address,
        contact_number: data.contact_number,
        address: data.address,
        due_date: data.dueDate,
        invoice_date: data.invoiceDate,
        tax: data.tax || 0, // optional
        sub_total: data.sub_total || 0, // optional
        total_amount: data.total_amount || 0, // optional
        invoice_number: data.invoice_number || "", // optional
        comments: data.comments,
        bill_to: data.address || "", // optional
        shipping_date: data.shippingDate,
        bol: data.bolNo,
        hs_code: data.hsCode,
        orgin_port: data.orgin_port || "",
        destination_port: data.destination_port || "",
        booking_id: data.bookingNo || "",
        incoterm: data.incoterm || "",
      },
      items: itemsDetails,
      // attachments: data.attachment ? [data.attachment] : [],
    };
    const fileDetails = attachments.filter((file) => file instanceof File);

    try {
      const response = await createQuickbooksInvoice(payload, fileDetails);
      if (response?.message?.status_code === 200) {
        toast.success("Invoice generated successfully!");
        setShowConfirmDialog(false);
        setFormPayload(null);
        navigate(
          "/dashboard/customers/view-invoice/" + response?.message?.invoice_name
        );
      } else {
        toast.error(response?.message?.Message);
        setShowConfirmDialog(false);
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Failed to create invoice:", error);
      toast.error("Failed to generate invoice.");
      setShowConfirmDialog(false);
      setIsLoading(false);
    }
    setIsLoading(false);
  };
  return (
    <div className="p-1 space-y-6">
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="p-1 grid grid-cols-1 md:grid-cols-4 gap-6"
      >
        <Card className="md:col-span-3 space-y-3 p-8">
          <h3 className="text-lg font-medium text-orange-600  tracking-wide mb-1">
            Invoice Details
          </h3>
          <hr className="border-gray-300 mt-0" />
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Customer Name
            </label>
            <ComboBoxPair
              label=""
              value={watch("customer_name")}
              onChange={(val) => setValue("customer_name", val)}
              options={docketCustomerOptions}
              placeholder="Select Customer"
            />

            {/* <ComboBox
            label=""
            required={false}
            value={watch("vendorName")}
            onChange={(value) => {
              const selected = vendorsData?.message?.data?.vendors?.find(
                (v: any) => v.name === value
              );
              setSelectedVendor(selected);
              setValue("vendorName", value);
            }}
            options={
              vendorsData?.message?.data?.vendors
                ?.slice()
                .sort((a: any, b: any) =>
                  a.vendor_name.localeCompare(b.vendor_name)
                )
                .map((vendor: any) => ({
                  label: vendor.vendor_name,
                  value: vendor.name,
                }))
            }
            placeholder={
              vendorsData?.message?.data?.vendors?.find(
                (v: any) => v.name === watch("vendorName")
              )?.vendor_name || "Select Vendor"
            }
          /> */}
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div>
              <label
                htmlFor="invoiceDate"
                className="block text-sm font-medium text-gray-700 mb-2 block"
              >
                Invoice Date
              </label>
              <Input
                className="border border-gray-300 text-sm px-3"
                id="invoiceDate"
                {...register("invoiceDate")}
                type="date"
                //required={id ? false : true}
              />
            </div>
            <div>
              <label
                htmlFor="bookingNo"
                className="block text-sm font-medium text-gray-700 mb-2 block"
              >
                Booking #
              </label>
              <Input
                className="border border-gray-300 text-sm px-3"
                id="bookingNo"
                {...register("bookingNo")}
                //disabled={id ? true : false}
              />
            </div>
            <div>
              <label
                htmlFor="hsCode"
                className="block text-sm font-medium text-gray-700 mb-2 block"
              >
                HS Code
              </label>
              <Input
                className="border border-gray-300 text-sm px-3"
                id="hsCode"
                {...register("hsCode")}
                //disabled={id ? true : false}
              />
            </div>
            <div>
              {/* {id ? ( */}
                <>
                  <label
                    htmlFor="orgin_port"
                    className="block text-sm font-medium text-gray-700 mb-2 block"
                  >
                    Origin Port
                  </label>
                  <Input
                    className="border border-gray-300 text-sm px-3"
                    id="orgin_port"
                    {...register("orgin_port")}
                    //disabled={id ? true : false}
                  />
                </>
              {/* ) : (
                <FormProvider {...methods}>
                  <FormField
                    control={control}
                    name="orgin_port"
                    render={({ field }) => {
                      const { value, onChange } = field;
                      const displayValue =
                        typeof value === "string" ? value : value?.location;

                      return (
                        <FormItem className="w-full">
                          <FormLabel className="flex justify-between items-center">
                            <span className="text-sm text-gray-700 font-medium">
                              Origin Port
                            </span>
                            {value && (
                              <span
                                className="text-sm text-muted-foreground underline cursor-pointer"
                                onClick={() => {
                                  setValue("orgin_port", "");
                                  setShownOriginValue("");
                                }}
                              >
                                Clear
                              </span>
                            )}
                          </FormLabel>
                          <FormControl>
                            <Popover
                              open={originPortOpen}
                              onOpenChange={setOriginPortOpen}
                            >
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  className="justify-between w-full overflow-hidden h-11"
                                >
                                  {displayValue || "Select Location..."}
                                  <ChevronsUpDown className="opacity-50" />
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-full p-0">
                                <Command>
                                  <CommandInput
                                    placeholder="Search Location..."
                                    value={originPortQuery}
                                    onValueChange={setOriginPortQuery}
                                    className="h-9"
                                  />
                                  <CommandList>
                                    <CommandEmpty>
                                      {isOriginFetching ? (
                                        <div className="flex justify-center w-full">
                                          <SpinnerLoader />
                                        </div>
                                      ) : (
                                        "No Location Found."
                                      )}
                                    </CommandEmpty>
                                    <CommandGroup>
                                      {originPortData?.message?.results?.map(
                                        (location) => {
                                          const locationStr = `${
                                            location.location_name
                                          }, ${
                                            location.sub_division
                                              ? location.sub_division + ", "
                                              : ""
                                          }${location.country} (${
                                            location.locode
                                          })`;

                                          return (
                                            <CommandItem
                                              key={location.name}
                                              value={locationStr}
                                              onSelect={() => {
                                                onChange(locationStr); // Store just the string
                                                setShownOriginValue(
                                                  locationStr
                                                );
                                                setOriginPortOpen(false);
                                              }}
                                            >
                                              {locationStr}
                                              <Check
                                                className={`ml-auto ${
                                                  value === locationStr
                                                    ? "opacity-100"
                                                    : "opacity-0"
                                                }`}
                                              />
                                            </CommandItem>
                                          );
                                        }
                                      )}
                                    </CommandGroup>
                                  </CommandList>
                                </Command>
                              </PopoverContent>
                            </Popover>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                </FormProvider>
              )} */}
            </div>
            <div>
              {/* {id ? ( */}
                <>
                  <label
                    htmlFor="destination_port"
                    className="block text-sm font-medium text-gray-700 mb-2 block"
                  >
                    Destination Port
                  </label>
                  <Input
                    className="border border-gray-300 text-sm px-3"
                    id="destination_port"
                    {...register("destination_port")}
                    //disabled={id ? true : false}
                  />
                </>
              {/* ) : (
                <FormProvider {...methods}>
                  <FormField
                    control={control}
                    name="destination_port"
                    render={({ field }) => {
                      const { value, onChange } = field;
                      const displayValue =
                        typeof value === "string" ? value : value?.location;

                      return (
                        <FormItem className="w-full">
                          <FormLabel className="flex justify-between items-center">
                            <span className="text-sm text-gray-700 font-medium">
                              Destination Port
                            </span>
                            {value && (
                              <span
                                className="text-sm text-muted-foreground underline cursor-pointer"
                                onClick={() => {
                                  setValue("destination_port", "");
                                  setShownDestinationValue("");
                                }}
                              >
                                Clear
                              </span>
                            )}
                          </FormLabel>
                          <FormControl>
                            <Popover
                              open={destinationPortOpen}
                              onOpenChange={setPortOfDestinationOpen}
                            >
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  className="justify-between w-full overflow-hidden h-11"
                                >
                                  {displayValue || "Select Destination..."}
                                  <ChevronsUpDown className="opacity-50" />
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-full p-0">
                                <Command>
                                  <CommandInput
                                    placeholder="Search Location..."
                                    value={destinationPortQuery}
                                    onValueChange={setDestinationPortQuery}
                                    className="h-9"
                                  />
                                  <CommandList>
                                    <CommandEmpty>
                                      {isDestinationFetching ? (
                                        <div className="flex justify-center w-full">
                                          <SpinnerLoader />
                                        </div>
                                      ) : (
                                        "No Location Found."
                                      )}
                                    </CommandEmpty>
                                    <CommandGroup>
                                      {destinationPortData?.message?.results?.map(
                                        (location) => {
                                          const locationStr = `${
                                            location.location_name
                                          }, ${
                                            location.sub_division
                                              ? location.sub_division + ", "
                                              : ""
                                          }${location.country} (${
                                            location.locode
                                          })`;

                                          return (
                                            <CommandItem
                                              key={location.name}
                                              value={locationStr}
                                              onSelect={() => {
                                                onChange(locationStr); // Store only string
                                                setShownDestinationValue(
                                                  locationStr
                                                );
                                                setPortOfDestinationOpen(false);
                                              }}
                                            >
                                              {locationStr}
                                              <Check
                                                className={`ml-auto ${
                                                  value === locationStr
                                                    ? "opacity-100"
                                                    : "opacity-0"
                                                }`}
                                              />
                                            </CommandItem>
                                          );
                                        }
                                      )}
                                    </CommandGroup>
                                  </CommandList>
                                </Command>
                              </PopoverContent>
                            </Popover>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                </FormProvider>
              )} */}
            </div>
            <div>
              <label
                htmlFor="bolNo"
                className="block text-sm font-medium text-gray-700 mb-2 block"
              >
                Bol#
              </label>
              <Input
                className="border border-gray-300 text-sm px-3"
                id="bolNo"
                {...register("bolNo")}
               // disabled={id ? true : false}
              />
            </div>
            <div>
              <label
                htmlFor="shippingDate"
                className="block text-sm font-medium text-gray-700 mb-2 block"
              >
                Shipping Date
              </label>
              <Input
                className="border border-gray-300 text-sm px-3"
                id="shippingDate"
                type="date"
                {...register("shippingDate")}
                //disabled={id ? true : false}
              />
            </div>
            <div>
              <label
                htmlFor="dueDate"
                className="block text-sm font-medium text-gray-700 mb-2 block"
              >
                Due Date
              </label>
              <Input
                className="border border-gray-300 text-sm px-3"
                id="dueDate"
                type="date"
                {...register("dueDate")}
                //required
              />
            </div>
            <div>
              <label
                htmlFor="incoterm"
                className="block text-sm font-medium text-gray-700 mb-2 block"
              >
                Incoterm
              </label>
              {/* {id ? (
                <Input
                  className="border border-gray-300 text-sm px-3"
                  id="incoterm"
                  {...register("incoterm")}
                  disabled
                />
              ) : ( */}
                <Controller
                  control={control}
                  name="incoterm"
                  render={({ field }) => (
                    <Select value={field.value} onValueChange={field.onChange}>
                      <SelectTrigger className="w-full border border-gray-300 text-sm px-3 h-10">
                        <SelectValue placeholder="Select Incoterm" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="CNF">CNF</SelectItem>
                        <SelectItem value="FOB">FOB</SelectItem>
                        <SelectItem value="CIF">CIF</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                />
              {/* )} */}
            </div>
          </div>

          <h3 className="text-lg font-medium">Items</h3>
          <div className="w-full ">
            <div className="min-w-[750px] space-y-2">
              <div className="flex items-center gap-3 text-sm font-medium text-muted-foreground px-1">
                <span className="w-[200px] text-black">Product/Services</span>
                <span className="w-[300px] text-black">
                  Product Description
                </span>
                <span className="w-[80px] text-black">Qty</span>
                <span className="w-[120px] text-black">UOM</span>
                <span className="w-[80px] text-black">Rate</span>
                <span className="w-[80px] text-black">Amount</span>
                <span className="w-[40px]"></span>
              </div>

              {fields.map((field, index) => {
                const qty = watch(`items.${index}.qty`) || 0;
                const unit_price = watch(`items.${index}.unit_price`) || 0;
                const amount = (qty * unit_price).toFixed(3);

                return (
                  <div
                    key={field.id}
                    className="flex items-center gap-3 px-2 py-2 h-auto"
                  >
                    <Popover>
                      <PopoverTrigger asChild>
                        <button
                            type="button"
                            className="w-[200px] min-w-[200px] max-h-[50px] border border-gray-300 rounded-sm px-3 py-2 flex justify-between items-start text-sm text-left whitespace-normal break-words overflow-y-auto"
                          >
                            <span className="flex-1 pr-2 overflow-y-auto max-h-[30px] leading-snug">
                            {itemOptions.find(
                              (item: any) =>
                                item.value ===
                                watch(`items.${index}.product_services`)
                            )?.label || "Product/Service"}
                          </span>
                          <ChevronsUpDown className="h-4 w-4 ml-2 opacity-50" />
                        </button>
                      </PopoverTrigger>
                      <PopoverContent className="w-[300px] p-0">
                        <Command>
                          <CommandInput
                            placeholder="Search product/service..."
                            onValueChange={(val) => setProductSearch(val)}
                            className="h-9"
                          />
                          <CommandList>
                            <CommandEmpty>
                              {productSearch.length === 0
                                ? "Type to search..."
                                : quickbooksItemsDataIsLoading
                                ? "Loading..."
                                : "No items found."}
                            </CommandEmpty>
                            <CommandGroup>
                              {quickbooksItemsData?.message?.items?.map(
                                (item: any) => (
                                  <CommandItem
                                    key={item.id}
                                    value={item.name}
                                    onSelect={() => {
                                      setValue(
                                        `items.${index}.product_services`,
                                        item.name
                                      );
                                      setValue(
                                        `items.${index}.product_description`,
                                        item.description || ""
                                      );
                                      setValue(
                                        `items.${index}.unit_price`,
                                        item.unit_price || 0
                                      );
                                      setValue(
                                        `items.${index}.item_ref_id`,
                                        item.id || ""
                                      );
                                    }}
                                  >
                                    {item.name}
                                    {watch(
                                      `items.${index}.product_services`
                                    ) === item.name && (
                                      <Check className="ml-auto h-4 w-4 text-primary" />
                                    )}
                                  </CommandItem>
                                )
                              )}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>

                    <Input
                      {...register(`items.${index}.product_description`, {
                        validate: (val) => {
                          const service = watch(
                            `items.${index}.product_services`
                          );
                          if (service && !val?.trim()) {
                            toast.error(
                              "Description is required when product/service is selected."
                            );
                            return false;
                          }
                          return true;
                        },
                      })}
                      placeholder="Product Description"
                      className="w-[300px]"
                    />
                    <Input
                      {...register(`items.${index}.qty`, {
                        min: {
                          value: 0,
                          message: "Qty must be 0 or greater",
                        },
                        setValueAs: (v) => (v === "" ? 0 : parseFloat(v)),
                      })}
                      min={0}
                      type="number"
                      placeholder="Qty"
                      className="w-[80px] appearance-none [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                      inputMode="decimal"
                      onBlur={(e) => {
                        const val = e.target.value;
                        if (val && val !== "0") {
                          // Strip leading zeros before a digit (e.g., "01.5" becomes "1.5")
                          e.target.value = val.replace(/^0+(\d)/, "$1");
                        }
                      }}
                    />
                    <div className="w-[120px]">
                      <Controller
                        control={control}
                        name={`items.${index}.uom`}
                        render={({ field }) => (
                          <Select
                            value={field.value}
                            onValueChange={field.onChange}
                          >
                            <SelectTrigger className="h-10 w-[120px]">
                              <SelectValue placeholder="UOM" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Kgs">Kgs</SelectItem>
                              <SelectItem value="MT">MT</SelectItem>
                              <SelectItem value="USt ton">USt ton</SelectItem>
                              <SelectItem value="Lbs">Lbs</SelectItem>
                              <SelectItem value="Container">
                                Container
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        )}
                      />
                    </div>
                    <Input
                      {...register(`items.${index}.unit_price`, {
                        min: {
                          value: 0,
                          message: "Rate must be 0 or greater",
                        },
                        setValueAs: (v) => (v === "" ? 0 : parseFloat(v)),
                      })}
                      type="number"
                      min={0}
                      step="0.01"
                      placeholder="Rate"
                      className="w-[80px] appearance-none [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                      inputMode="decimal"
                      onBlur={(e) => {
                        const val = e.target.value;
                        if (val && val !== "0") {
                          // Remove leading zeros before digit (preserve decimals like 0.5)
                          e.target.value = val.replace(/^0+(\d)/, "$1");
                        }
                      }}
                    />
                    <Input value={amount} readOnly className="w-[80px]" />
                    <Button
                      type="button"
                      onClick={() => remove(index)}
                      variant="ghost"
                      size="icon"
                      className="text-muted-foreground"
                    >
                      <Trash2 className="w-6 h-6 text-black" />
                    </Button>
                  </div>
                );
              })}

              <Button
                variant="ghost"
                className="text-orange-600 flex items-center gap-1 text-sm"
                type="button"
                onClick={() =>
                  append({
                    product_services: "",
                    product_description: "",
                    qty: 1,
                    uom: "",
                    unit_price: 0,
                    amount: 0,
                    item_ref_id: "",
                  })
                }
              >
                <PlusCircle className="h-4 w-4" />
                Add Item
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <label
              htmlFor="comments"
              className="block text-sm font-medium text-gray-700"
            >
              Comments
            </label>
            <Textarea
              id="comments"
              {...register("comments")}
              placeholder="Comments"
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Attach File
            </label>

            {attachments.map((file, index) => (
              <div key={index} className="flex w-full items-center space-x-2">
                <div className="flex flex-grow items-center border border-gray-300 rounded-md overflow-hidden">
                  {/* CHOOSE FILE Button */}
                  <label className="flex items-center gap-2 px-3 py-3 bg-white border-r border-gray-300 text-sm cursor-pointer text-gray-700">
                    <Image className="w-5 h-5 text-gray-600" />
                    <span className="font-medium">CHOOSE FILE</span>
                    <input
                      type="file"
                      className="hidden"
                      onChange={(e) => handleAttachmentChange(e, index)}
                    />
                  </label>

                  {/* File name input */}
                  {/* <input
                    type="text"
                    readOnly
                    className="flex-1 px-4 py-2 text-sm bg-gray-50 text-gray-700 placeholder:text-gray-400 border-0 focus:outline-none"
                    placeholder="Upload Attach File Here...."
                    value={file?.name || ""}
                  /> */}
                  <div className="flex-1 flex items-center justify-between bg-gray-50 px-4 py-2 text-sm">
                    <span className="truncate text-gray-700">
                      {file?.name || "Upload Attach File Here...."}
                    </span>

                    {file && (
                      <button
                        type="button"
                        onClick={() => {
                          const updated = [...attachments];
                          updated[index] = undefined as unknown as File;
                          setAttachments(updated);
                        }}
                        className="text-gray-500 hover:text-red-600 ml-2"
                        title="Remove file"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>

                {/* Remove Button */}
                {attachments.length > 1 && (
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() =>
                      setAttachments(attachments.filter((_, i) => i !== index))
                    }
                  >
                    <Minus />
                  </Button>
                )}
                {/* Add Button (only on last row) */}
                {index === attachments.length - 1 && (
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() =>
                      setAttachments([
                        ...attachments,
                        undefined as unknown as File,
                      ])
                    }
                  >
                    <PlusCircle />
                  </Button>
                )}
              </div>
            ))}

            {/* Empty State */}
            {attachments.length === 0 && (
              <div className="flex w-full items-center space-x-2">
                <div className="flex flex-grow items-center border border-gray-300 rounded-md overflow-hidden">
                  <label className="flex items-center gap-2 px-3 py-3 bg-white border-r border-gray-300 text-sm cursor-pointer text-gray-700">
                    <Image className="w-5 h-5 text-gray-600" />
                    <span className="font-medium">CHOOSE FILE</span>
                    <input
                      type="file"
                      className="hidden"
                      onChange={(e) => handleAttachmentChange(e, 0)}
                    />
                  </label>
                  <input
                    type="text"
                    readOnly
                    className="flex-1 px-4 py-2 text-sm bg-gray-50 text-gray-700 placeholder:text-gray-400 border-0 focus:outline-none"
                    placeholder="Upload Attach File Here...."
                    value=""
                  />
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  className="shrink-0"
                  onClick={handleAddAttachment}
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
            )}
          </div>
        </Card>

        <Card className="p-8 space-y-3">
          <h3 className="text-lg font-medium text-orange-600  tracking-wide mb-1">
            Customer Details
          </h3>
          <hr className="border-gray-300 mt-0" />
          <div>
            {/* <label className="text-sm text-gray-700 font-medium mb-2 block">
              Customer In Quickbooks
            </label> */}
            <ComboBoxPair
              label="Customer In QuickBooks"
              value={watch("quickbooks_customer_id")}
              onChange={(val) => setValue("quickbooks_customer_id", val)}
              options={customerOptions}
              placeholder="Select Customer"
              //error={errors.customerName?.message}
            />
          </div>

          {/* Bill To */}
          <div>
            <label className="text-sm text-gray-700 font-medium mb-2 block">
              Bill To
            </label>
            <Textarea {...register("address")} placeholder="Bill To" />
          </div>

          {/* Email Address */}
          <div>
            <label className="text-sm text-gray-700 font-medium mb-2 block">
              Email Address
            </label>
            <Input {...register("email_address")} placeholder="Email Address" />
          </div>

          {/* Contact */}
          <div>
            <label className="text-sm text-gray-700 font-medium mb-2 block">
              Contact
            </label>
            <Input {...register("contact_number")} placeholder="Contact" />
          </div>

          {/* <div className="space-y-1 w-full max-w-sm">
            <div className="flex justify-between mb-3">
              <span className="text-gray-900 font-semibold">Sub Total:</span>
              <span className="text-black font-semibold ">0.00</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-900 font-semibold">
                Tax <span className="text-gray-500 font-semibold">(18%)</span>:
              </span>
              <span className="text-black font-semibold">0.00</span>
            </div>
            <hr className="border-gray-300 mt-3 mb-5" />
            <div className="flex justify-between">
              <span className="text-gray-900 font-semibold">Total Amount:</span>
              <span className="text-black font-semibold">0.00</span>
            </div>
          </div> */}

          <Button type="submit" className="w-full">
            <Save size={20} /> Generate Invoice
          </Button>
        </Card>

        <AlertDialog
          open={showConfirmDialog}
          onOpenChange={setShowConfirmDialog}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Generate Invoice</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to generate this Invoice?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                className={isLoading ? "cursor-not-allowed" : ""}
                disabled={isLoading}
                onClick={(e) => {
                  e.preventDefault();
                  handleConfirmSubmit();
                }}
              >
                Yes, Generate
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </form>
    </div>
  );
}
