import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { useParams } from "react-router-dom";
import { useQuery, useMutation } from "@tanstack/react-query";
import {
  getInvoiceDetails,
  generateInvoicePDF,
} from "@/services/admin/invoiceGenerate";
import { useNavigate } from "react-router-dom";
import { Eye, Image, Pen } from "lucide-react";
import { ViewAttachedFiles } from "./viewAttachedFile";
import React from "react";
import { Card } from "@/components/ui/card";
import dayjs from "dayjs";

export default function InvoiceSummary() {
  const baseUrl = import.meta.env.VITE_BACKEND_URL || "";
  const navigate = useNavigate();
  const { id } = useParams();
  const {
    data: invoiceData,
    // isFetching: isInvoiceFetching,
    // refetch: refetchInvoice,
  } = useQuery({
    queryKey: ["getInvoiceDetails", { invoiceId: id }],
    queryFn: () => getInvoiceDetails(id as string),
    enabled: !!id,
  });
  const [previewOpen, setPreviewOpen] = React.useState(false);
  const [selectedFile, setSelectedFile] = React.useState<{
    name: string;
    license: string;
  } | null>(null);
  const handleFileClick = (file) => {
    setSelectedFile({
      name: file.file_name,
      license: file.file_url,
    });
    setPreviewOpen(true);
  };
  return (
    <div className="p-1 space-y-6">
      <div className="p-1 grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Invoice Details */}
        <Card className="md:col-span-3 space-y-3 p-8">
          <h3 className="text-lg font-medium text-orange-600 tracking-wide mb-1">
            Invoice Details
          </h3>
          <hr className="border-gray-300 mt-0" />

          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
            <div>
              <strong>Customer Name:</strong>{" "}
              {invoiceData?.message?.data?.customer_name}
            </div>
            <div>
              <strong>Invoice #:</strong>{" "}
              {invoiceData?.message?.data?.invoice_number}
            </div>
            <div>
              <strong>Invoice Date:</strong>{" "}
              {dayjs(invoiceData?.message?.data?.invoice_date).format(
                "MMM-DD-YYYY"
              )}
            </div>
            <div>
              <strong>Booking #:</strong>{" "}
              {invoiceData?.message?.data?.booking_id}
            </div>
            <div>
              <strong>HS Code:</strong> {invoiceData?.message?.data?.hs_code}
            </div>
            <div>
              <strong>Origin Port:</strong>{" "}
              {invoiceData?.message?.data?.origin_port}
            </div>
            <div>
              <strong>Destination Port:</strong>{" "}
              {invoiceData?.message?.data?.destination_port}
            </div>
            <div>
              <strong>BOL #:</strong> {invoiceData?.message?.data?.bol}
            </div>
            <div>
              <strong>Shipping Date:</strong>{" "}
              {dayjs(invoiceData?.message?.data?.shipping_date).format(
                "MMM-DD-YYYY"
              )}
            </div>
            <div>
              <strong>Due Date:</strong>{" "}
              {dayjs(invoiceData?.message?.data?.due_date).format(
                "MMM-DD-YYYY"
              )}
            </div>
            <div>
              <strong>Incoterm:</strong> {invoiceData?.message?.data?.incoterm}
            </div>
          </div>

          {/* Items */}
          <h3 className="text-lg font-medium mt-6">Items</h3>
          <div className="w-full overflow-x-auto">
            <table className="min-w-[750px] text-sm w-full border border-gray-300">
              <thead className="bg-gray-100">
                <tr>
                  <th className="text-left px-3 py-2">Product/Service</th>
                  <th className="text-left px-3 py-2">Description</th>
                  <th className="text-left px-3 py-2">Qty</th>
                  <th className="text-left px-3 py-2">UOM</th>
                  <th className="text-left px-3 py-2">Rate</th>
                  <th className="text-left px-3 py-2">Amount</th>
                </tr>
              </thead>
              <tbody>
                {invoiceData?.message?.data?.items.map((item, index) => (
                  <tr key={index} className="border-t">
                    <td className="px-3 py-2">{item.product_services}</td>
                    <td className="px-3 py-2">{item.product_description}</td>
                    <td className="px-3 py-2">{item.quantity}</td>
                    <td className="px-3 py-2">{item.uom}</td>
                    <td className="px-3 py-2">{item.rate}</td>
                    <td className="px-3 py-2">{item.amount}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Comments */}
          <div className="mt-4">
            <strong>Comments:</strong>
            <p className="text-sm text-gray-800 whitespace-pre-line mt-1">
              {invoiceData?.message?.data.comments}
            </p>
          </div>

          <div className="mt-4">
            <strong>Invoice PDF</strong>
            <br />
            {invoiceData?.message?.data?.attachments?.filter(
              (file: any) => file.label === "Generated PDF"
            )?.length > 0 ? (
              invoiceData.message.data.attachments
                .filter((file: any) => file.label === "Generated PDF")
                .map((file: any, index: number) => (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    <Image className="w-5 h-5 text-gray-600" />
                    <a
                      href={`${baseUrl}${file.file_url}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {file.file_name}
                    </a>
                  </div>
                ))
            ) : (
              <p className="text-sm text-gray-500">No attachments</p>
            )}
          </div>

          <div className="space-y-2 mt-4">
            <strong className="block text-sm font-medium text-gray-700">
              Attached Files
            </strong>
            {invoiceData?.message?.data?.attachments?.filter(
              (file: any) => file.label === "Uploaded Attachment"
            )?.length > 0 ? (
              invoiceData.message.data.attachments
                .filter((file: any) => file.label === "Uploaded Attachment")
                .map((file: any, index: number) => (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    <Image className="w-5 h-5 text-gray-600" />
                    <span
                      onClick={() => handleFileClick(file)}
                      className="text-blue-600 hover:underline cursor-pointer"
                    >
                      {file.file_name}
                    </span>
                  </div>
                ))
            ) : (
              <p className="text-sm text-gray-500">No attachments</p>
            )}
          </div>
        </Card>

        {/* Customer Details */}
        <Card className="p-8 space-y-3">
          <h3 className="text-lg font-medium text-orange-600 tracking-wide mb-1">
            Customer Details
          </h3>
          <hr className="border-gray-300 mt-0" />
          <div className="text-sm space-y-2">
            <div>
              <strong>Customer in QuickBooks:</strong>{" "}
              {invoiceData?.message?.data?.quickbooks_customer_name}
            </div>
            <div>
              <strong>Bill To:</strong>{" "}
              <pre className="whitespace-pre-wrap">
                {invoiceData?.message?.data?.bill_to}
              </pre>
            </div>
            <div>
              <strong>Email:</strong> {invoiceData?.message?.data?.email}
            </div>
            <div>
              <strong>Contact:</strong> {invoiceData?.message?.data?.contact}
            </div>
          </div>

          {/* Totals */}
          <div className="space-y-1 w-full max-w-sm text-sm mt-4">
            <div className="flex justify-between">
              <span className="text-gray-900 font-semibold">Sub Total:</span>
              <span className="text-black font-semibold">
                {invoiceData?.message?.data?.sub_total ?? "0.00"}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-900 font-semibold">
                Tax <span className="text-gray-500 font-semibold"></span>:
              </span>
              <span className="text-black font-semibold">
                {invoiceData?.message?.data?.tax ?? "0.00"}
              </span>
            </div>
            <hr className="border-gray-300 my-3" />
            <div className="flex justify-between">
              <span className="text-gray-900 font-semibold">Total Amount:</span>
              <span className="text-black font-semibold">
                {invoiceData?.message?.data?.total_amount ?? "0.00"}
              </span>
            </div>
          </div>
          {/* {!invoiceData?.message?.data?.invoice_pdf_link && ( */}
          {/* <Button
              type="button"
              className="w-full"
              onClick={() =>
                handleGeneratePDF(invoiceData?.message?.data?.invoice_id)
              }
              disabled={isGeneratingPDF}
            >
              <FileText size={20} className="mr-2" />
              {isGeneratingPDF ? "Generating..." : "Generate Invoice PDF"}
            </Button> */}
          {/* )} */}
          {/* {invoiceData?.message?.data?.carrier_booking_number &&
            invoiceData?.message?.data?.docket_id && (
              <Button
                type="button"
                className="w-full"
                onClick={() =>
                  navigate(
                    `/dashboard/customers/customer-docket-view/${invoiceData?.message?.data?.docket_id}?carrierBooking=${invoiceData?.message?.data?.carrier_booking_number}`
                  )
                }
              >
                <Eye size={20} className="mr-2" />
                {"Back To Docket"}
              </Button>
            )} */}
          {/* <Button
                type="button"
                className="w-full"
                onClick={() =>
                  navigate(
                    `/dashboard/customers/update-invoice/${id}?docketId=${invoiceData?.message?.data?.docket_id}`
                  )
                }
              >
                <Pen size={20} className="mr-2" />
                {"Edit Invoice"}
              </Button> */}
          <button
            onClick={() => navigate(-1)}
            className="mb-2 px-4 py-2 bg-gray-200 text-sm rounded hover:bg-gray-300 transition"
          >
            ← Back
          </button>
        </Card>

        <ViewAttachedFiles
          open={previewOpen}
          selectedFile={selectedFile}
          onOpenChange={setPreviewOpen}
          setSelectedFile={setSelectedFile}
        />
      </div>
    </div>
  );
}
