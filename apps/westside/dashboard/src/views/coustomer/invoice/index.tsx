import { useState, useEffect, useRef } from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import {
  Eye,
  Printer,
  Download,
  Search,
  RefreshCcw,
  ChevronLeft,
  ChevronRight,
  FileSearch2,
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { useNavigate, useSearchParams } from "react-router-dom";
import { listInvoice } from "@/services/admin/invoiceGenerate";
import { useQuery } from "@tanstack/react-query";
import dayjs from "dayjs";

type InvoiceStatus =
  | "Paid"
  | "Unpaid"
  | "Open"
  | "Overdue"
  | "Viewed"
  | "Pending Payment";

const statusColor: Record<InvoiceStatus, string> = {
  Paid: "bg-green-50 text-green-600 border border-green-200",
  Unpaid: "bg-orange-50 text-orange-600 border border-orange-200",
  Open: "bg-violet-50 text-violet-600 border border-violet-200",
  Overdue: "bg-red-50 text-red-600 border border-red-200",
  Viewed: "bg-blue-50 text-blue-600 border border-blue-200",
  "Pending Payment": "bg-yellow-50 text-yellow-600 border border-yellow-200",
};
function getStatusColor(status: string): string {
  if (status in statusColor) {
    return statusColor[status as InvoiceStatus];
  }
  return "bg-violet-50 text-violet-600 border border-violet-200";
}
export default function CoustomerInvoiceList() {
  const [searchParams, setSearchParams] = useSearchParams();
  const statusFilter = searchParams.get("status") || "";
  const dateFilter = searchParams.get("date") || "";
  const search = searchParams.get("search") || "";
  const [currentPage, setCurrentPage] = useState(
    parseInt(searchParams.get("page") || "1", 10) || 1
  );
  const rowsPerPage = 10;
  const [selectedInvoices, setSelectedInvoices] = useState<number[]>([]);

  const toggleCheckbox = (index: number) => {
    setSelectedInvoices((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
    );
  };

  const selectAllRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();
  useEffect(() => {
    if (selectAllRef.current) {
      const isIndeterminate =
        selectedInvoices.length > 0 && selectedInvoices.length < ["hi"].length;
      selectAllRef.current.indeterminate = isIndeterminate;
    }
  }, [selectedInvoices]);
  const isSelected = (index: number) => selectedInvoices.includes(index);

  const {
    data: invoicesList,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["listInvoice", statusFilter, dateFilter, search],
    queryFn: () =>
      listInvoice({
        ...(statusFilter && { status_filter: statusFilter }),
        ...(dateFilter && { date: dateFilter }),
        ...(search && { search }),
      }),
  });
  const totalCount = invoicesList?.message?.invoices?.length || 0;
  const totalPages = Math.ceil(totalCount / rowsPerPage);

  const paginatedData =
    invoicesList?.message?.invoices?.slice(
      (currentPage - 1) * rowsPerPage,
      currentPage * rowsPerPage
    ) || [];
  return (
    <Card className="w-full shadow-sm">
      <CardContent className="p-0">
        <div className="flex flex-wrap justify-between items-center gap-3 p-4">
          {/* Status Tabs */}
          <div className="flex flex-wrap items-center gap-4">
            <Button variant="outline">
              All{" "}
              <span className="ml-1 text-gray-500">
                {invoicesList?.message?.counts?.total || 0}
              </span>
            </Button>
            <Button variant="ghost" className="text-[#5B77FF]">
              Open Invoice{" "}
              <span className="ml-1 text-[#5B77FF]">
                {invoicesList?.message?.counts?.open_invoice || 0}
              </span>
            </Button>
            <Button variant="ghost" className="text-[#42C37B]">
              Paid{" "}
              <span className="ml-1 text-[#42C37B]">
                {invoicesList?.message?.counts?.paid || 0}
              </span>
            </Button>
            <Button variant="ghost" className="text-[#FF6B6B]">
              Overdue{" "}
              <span className="ml-1 text-[#FF6B6B]">
                {invoicesList?.message?.counts?.overdue || 0}
              </span>
            </Button>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap items-center gap-3">
            {/* Date Filter */}
            <input
              type="date"
              className="border border-gray-300 rounded-md px-3 text-sm h-[40px]"
              value={dateFilter}
              onChange={(e) => {
                setSearchParams((prev) => {
                  const newParams = new URLSearchParams(prev);
                  newParams.set("date", e.target.value);
                  newParams.set("page", "1");
                  return newParams;
                });
              }}
            />

            {/* Search Input */}
            <div className="relative">
              <Input
                className="pr-10 text-sm h-[40px]"
                placeholder="Search here"
                value={search}
                onChange={(e) => {
                  setSearchParams((prev) => {
                    const newParams = new URLSearchParams(prev);
                    newParams.set("search", e.target.value);
                    newParams.set("page", "1");
                    return newParams;
                  });
                }}
              />
              <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            </div>

            {/* Status Dropdown */}
            <select
              className="border border-gray-300 rounded-md px-2 text-sm h-[40px]"
              value={statusFilter}
              onChange={(e) => {
                setSearchParams((prev) => {
                  const newParams = new URLSearchParams(prev);
                  newParams.set("status", e.target.value);
                  newParams.set("page", "1");
                  return newParams;
                });
              }}
            >
              <option value="">Status</option>
              <option value="Viewed">Viewed</option>
              <option value="Open">Open Invoice</option>
              <option value="Paid">Paid</option>
              <option value="Overdue">Overdue</option>
            </select>
            <div className="relative flex">
              {(statusFilter || search || dateFilter) && (
                <Button
                  variant="outline"
                  className="h-11"
                  onClick={() => {
                    setSearchParams({});
                    setCurrentPage(1);
                  }}
                >
                  <RefreshCcw /> Clear Filters
                </Button>
              )}
            </div>
            {/* <Button
              onClick={() =>
                navigate(
                  `/dashboard/customers/generate-invoice?docketId=${nullId}`
                )
              }
              className="bg-foreground h-10 px-4 text-white text-sm"
            >
              <Plus className="mr-2 h-4 w-4" />
              Create Invoice
            </Button> */}
          </div>
        </div>

        <ScrollArea className="w-full">
          <Table className="table-auto w-full border-separate border-spacing-y-2 text-sm">
            <TableHeader>
              <TableRow className="bg-[#D3DAE7] h-[55px]">
                {/* <TableHead className="p-3 w-[3%]"> */}
                {/* Optional: master checkbox for select all */}
                {/* <input
                    type="checkbox"
                    // onChange={(e) =>
                      // setSelectedInvoices(
                        // e.target.checked ? invoices.map((_, idx) => idx) : []
                      // )
                    // }
                    // checked={selectedInvoices.length === invoices.length}
                  /> */}
                {/* </TableHead> */}
                <TableHead className="p-3 pl-6 font-bold text-[#191C36] w-[20%]">
                  CARRIER BOOKING NO.
                </TableHead>
                <TableHead className="p-3 pl-6 font-bold text-[#191C36] w-[12%]">
                  INVOICE NUMBER
                </TableHead>
                <TableHead className="p-3 pl-6 font-bold text-[#191C36] w-[12%]">
                  ORIGIN PORT
                </TableHead>
                <TableHead className="p-3 pl-6 font-bold text-[#191C36] w-[10%]">
                  ISSUE DATE
                </TableHead>
                <TableHead className="p-3 pl-6 font-bold text-[#191C36] w-[10%]">
                  DUE DATE
                </TableHead>
                <TableHead className="p-3 pl-6 font-bold text-[#191C36] w-[12%]">
                  AMOUNT
                </TableHead>
                <TableHead className="p-3 pl-6 font-bold text-[#191C36] w-[10%]">
                  STATUS
                </TableHead>
                <TableHead className="p-3 pl-6 font-bold text-[#191C36] w-[14%] text-center">
                  ACTION
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedData?.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={10} className="text-center py-8">
                    <div className="flex flex-col items-center justify-center gap-2">
                      <FileSearch2 className="w-10 h-10 text-gray-400" />
                      <span className="text-gray-500 font-medium">
                        No data found
                      </span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                paginatedData?.map((inv: any, idx: any) => (
                  <TableRow
                    key={idx}
                    className={`border border-[#D3DAE7] hover:bg-[#D3DAE7] transition duration-200 shadow-sm ${
                      isSelected(idx) ? "bg-blue-50" : ""
                    }`}
                  >
                    {/* <TableCell className="p-3 text-left">
                        <input
                          type="checkbox"
                          checked={isSelected(idx)}
                          onChange={() => toggleCheckbox(idx)}
                        />
                      </TableCell> */}
                    <TableCell className="p-3 pl-6">
                      <div className="flex items-center gap-3">
                        {/* Avatar hidden for now */}
                        <div className="flex flex-col">
                          <span className="font-medium">
                            {inv.carrier_booking_number || "N/A"}
                          </span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="p-3 pl-6">
                      {inv.invoice_number || "N/A"}
                    </TableCell>
                    <TableCell className="p-3 pl-6">
                      {inv.orgin_port || "N/A"}
                    </TableCell>
                    <TableCell className="p-3 pl-6">
                      {inv.invoice_date
                        ? dayjs(inv.invoice_date).format("MMM-DD-YYYY")
                        : "N/A"}
                    </TableCell>
                    <TableCell className="p-3 pl-6">
                      {inv.due_date
                        ? dayjs(inv.due_date).format("MMM-DD-YYYY")
                        : "N/A"}
                    </TableCell>
                    <TableCell className="p-3 pl-6">
                      {inv.total_amount ? inv.total_amount : "N/A"}
                    </TableCell>
                    <TableCell className="p-3 pl-6">
                      <span
                        className={`px-3 py-1 text-xs font-normal rounded-md ${getStatusColor(
                          inv.status
                        )}`}
                      >
                        {inv.status ?? "Open"}
                      </span>
                    </TableCell>
                    <TableCell className="p-3 pl-6 text-center">
                      <div className="flex justify-center gap-2">
                        <Button
                          size="icon"
                          variant="ghost"
                          onClick={() =>
                            navigate(
                              `/dashboard/customer/invoice-details-view/${inv.name}`
                            )
                          }
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        {/* <Button size="icon" variant="ghost">
                            <Printer className="h-4 w-4" />
                          </Button>
                          <Button size="icon" variant="ghost">
                            <Download className="h-4 w-4" />
                          </Button> */}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </CardContent>

      <div className="flex items-center justify-between mt-6">
        <div className="flex items-center gap-2 mx-auto sm:mx-0 sm:ml-auto">
          <Button
            className="rounded-lg px-3 py-2"
            variant="outline"
            onClick={() => {
              setCurrentPage((prev) => Math.max(prev - 1, 1));
              setSearchParams((prev) => {
                const newParams = new URLSearchParams(prev);
                newParams.set("page", String(currentPage - 1));
                return newParams;
              });
            }}
            disabled={currentPage === 1}
            size="sm"
          >
            <ChevronLeft className="text-black h-4 w-4" />
          </Button>
          <div className="flex gap-1">
            {Array.from({ length: Math.min(totalPages, 5) }).map((_, index) => {
              let page;
              if (totalPages <= 5) {
                page = index + 1;
              } else if (currentPage <= 3) {
                page = index + 1;
              } else if (currentPage >= totalPages - 2) {
                page = totalPages - 4 + index;
              } else {
                page = currentPage - 2 + index;
              }

              return (
                <Button
                  key={page}
                  onClick={() => {
                    setCurrentPage(page);
                    setSearchParams((prev) => {
                      const newParams = new URLSearchParams(prev);
                      newParams.set("page", String(page));
                      return newParams;
                    });
                  }}
                  className={`rounded-lg px-3 py-2 ${
                    page === currentPage
                      ? "bg-white text-black border"
                      : "text-gray-500"
                  }`}
                  variant="outline"
                  size="sm"
                >
                  {page}
                </Button>
              );
            })}
            {totalPages > 5 && currentPage < totalPages - 2 && (
              <>
                <span className="flex items-center px-2">...</span>
                <Button
                  onClick={() => {
                    setCurrentPage(totalPages);
                    setSearchParams((prev) => {
                      const newParams = new URLSearchParams(prev);
                      newParams.set("page", String(totalPages));
                      return newParams;
                    });
                  }}
                  className={`rounded-lg px-3 py-2 ${
                    totalPages === currentPage
                      ? "bg-white text-black border"
                      : "text-gray-500"
                  }`}
                  variant="outline"
                  size="sm"
                >
                  {totalPages}
                </Button>
              </>
            )}
          </div>
          <Button
            className="rounded-lg px-3 py-2"
            variant="outline"
            onClick={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            disabled={currentPage === totalPages}
            size="sm"
          >
            <ChevronRight className="text-black h-4 w-4" />
          </Button>
        </div>
      </div>
    </Card>
  );
}
